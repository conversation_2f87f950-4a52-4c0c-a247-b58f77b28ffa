import { ref, computed } from 'vue';

export interface PerformanceMetrics {
  renderTime: number;
  nodeCount: number;
  linkCount: number;
  memoryUsage: number;
  fps: number;
  lastUpdateTime: number;
}

export function usePerformanceMonitor() {
  // ===== 状态管理 =====
  const isMonitoring = ref<boolean>(false);
  const metrics = ref<PerformanceMetrics>({
    renderTime: 0,
    nodeCount: 0,
    linkCount: 0,
    memoryUsage: 0,
    fps: 0,
    lastUpdateTime: 0
  });

  // 性能计时器
  let renderStartTime = 0;
  let frameCount = 0;
  let fpsStartTime = 0;
  let animationFrameId: number | null = null;

  // ===== 核心监控方法 =====

  /**
   * 开始渲染计时
   */
  const startRenderTimer = (): void => {
    renderStartTime = performance.now();
  };

  /**
   * 结束渲染计时
   */
  const endRenderTimer = (): void => {
    if (renderStartTime > 0) {
      metrics.value.renderTime = performance.now() - renderStartTime;
      metrics.value.lastUpdateTime = Date.now();
      renderStartTime = 0;
      
      logPerformanceWarnings();
    }
  };

  /**
   * 更新节点和连接线数量
   */
  const updateCounts = (nodeCount: number, linkCount: number): void => {
    metrics.value.nodeCount = nodeCount;
    metrics.value.linkCount = linkCount;
  };

  /**
   * 更新内存使用情况
   */
  const updateMemoryUsage = (): void => {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      metrics.value.memoryUsage = Math.round(memInfo.usedJSHeapSize / 1024 / 1024 * 100) / 100;
    }
  };

  /**
   * FPS 监控
   */
  const startFPSMonitoring = (): void => {
    if (animationFrameId) return;

    frameCount = 0;
    fpsStartTime = performance.now();

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      const elapsed = currentTime - fpsStartTime;

      if (elapsed >= 1000) {
        metrics.value.fps = Math.round((frameCount * 1000) / elapsed);
        frameCount = 0;
        fpsStartTime = currentTime;
      }

      if (isMonitoring.value) {
        animationFrameId = requestAnimationFrame(measureFPS);
      }
    };

    animationFrameId = requestAnimationFrame(measureFPS);
  };

  /**
   * 停止 FPS 监控
   */
  const stopFPSMonitoring = (): void => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  };

  /**
   * 开始性能监控
   */
  const startMonitoring = (): void => {
    isMonitoring.value = true;
    startFPSMonitoring();
    
    // 定期更新内存使用情况
    const memoryInterval = setInterval(() => {
      if (isMonitoring.value) {
        updateMemoryUsage();
      } else {
        clearInterval(memoryInterval);
      }
    }, 1000);

    console.log('性能监控已启动');
  };

  /**
   * 停止性能监控
   */
  const stopMonitoring = (): void => {
    isMonitoring.value = false;
    stopFPSMonitoring();
    console.log('性能监控已停止');
  };

  /**
   * 重置性能指标
   */
  const resetMetrics = (): void => {
    metrics.value = {
      renderTime: 0,
      nodeCount: 0,
      linkCount: 0,
      memoryUsage: 0,
      fps: 0,
      lastUpdateTime: 0
    };
  };

  // ===== 性能分析 =====

  /**
   * 记录性能警告
   */
  const logPerformanceWarnings = (): void => {
    const { renderTime, nodeCount, fps, memoryUsage } = metrics.value;

    // 渲染时间警告
    if (renderTime > 2000) {
      console.warn(`⚠️ 渲染时间过长: ${renderTime.toFixed(2)}ms (节点数: ${nodeCount})`);
    }

    // FPS 警告
    if (fps > 0 && fps < 30) {
      console.warn(`⚠️ FPS 过低: ${fps} (目标: 60fps)`);
    }

    // 内存使用警告
    if (memoryUsage > 100) {
      console.warn(`⚠️ 内存使用过高: ${memoryUsage}MB`);
    }

    // 节点数量警告
    if (nodeCount > 1000) {
      console.warn(`⚠️ 节点数量过多: ${nodeCount} (建议: <1000)`);
    }
  };

  /**
   * 获取性能评级
   */
  const getPerformanceGrade = computed(() => {
    const { renderTime, fps, memoryUsage, nodeCount } = metrics.value;

    let score = 100;

    // 渲染时间评分 (权重: 30%)
    if (renderTime > 3000) score -= 30;
    else if (renderTime > 2000) score -= 20;
    else if (renderTime > 1000) score -= 10;

    // FPS 评分 (权重: 25%)
    if (fps > 0) {
      if (fps < 20) score -= 25;
      else if (fps < 40) score -= 15;
      else if (fps < 55) score -= 5;
    }

    // 内存使用评分 (权重: 20%)
    if (memoryUsage > 200) score -= 20;
    else if (memoryUsage > 150) score -= 15;
    else if (memoryUsage > 100) score -= 10;

    // 数据规模评分 (权重: 25%)
    if (nodeCount > 1500) score -= 25;
    else if (nodeCount > 1000) score -= 15;
    else if (nodeCount > 800) score -= 10;

    if (score >= 90) return { grade: 'A', color: '#52c41a', text: '优秀' };
    if (score >= 80) return { grade: 'B', color: '#1890ff', text: '良好' };
    if (score >= 70) return { grade: 'C', color: '#faad14', text: '一般' };
    if (score >= 60) return { grade: 'D', color: '#fa8c16', text: '较差' };
    return { grade: 'F', color: '#f5222d', text: '很差' };
  });

  /**
   * 生成性能报告
   */
  const generateReport = (): string => {
    const { renderTime, nodeCount, linkCount, fps, memoryUsage } = metrics.value;
    const grade = getPerformanceGrade.value;

    return `
性能报告 (${new Date().toLocaleString()})
=====================================
总体评级: ${grade.grade} (${grade.text})
渲染时间: ${renderTime.toFixed(2)}ms
节点数量: ${nodeCount}
连接线数量: ${linkCount}
FPS: ${fps}
内存使用: ${memoryUsage}MB

建议:
${renderTime > 2000 ? '- 考虑减少节点数量或优化模板\n' : ''}
${fps > 0 && fps < 30 ? '- 检查是否有阻塞主线程的操作\n' : ''}
${memoryUsage > 100 ? '- 检查是否有内存泄漏\n' : ''}
${nodeCount > 1000 ? '- 考虑使用分页或虚拟化\n' : ''}
=====================================
    `.trim();
  };

  /**
   * 导出性能数据
   */
  const exportMetrics = (): PerformanceMetrics & { grade: string; timestamp: number } => {
    return {
      ...metrics.value,
      grade: getPerformanceGrade.value.grade,
      timestamp: Date.now()
    };
  };

  // ===== 便捷方法 =====

  /**
   * 测量函数执行时间
   */
  const measureFunction = async <T>(
    name: string,
    fn: () => T | Promise<T>
  ): Promise<T> => {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    return result;
  };

  /**
   * 批量性能测试
   */
  const runPerformanceTest = async (
    tests: Array<{ name: string; fn: () => void | Promise<void> }>
  ): Promise<void> => {
    console.log('🧪 开始性能测试...');
    
    for (const test of tests) {
      await measureFunction(test.name, test.fn);
    }
    
    console.log('✅ 性能测试完成');
    console.log(generateReport());
  };

  return {
    // 状态
    isMonitoring: computed(() => isMonitoring.value),
    metrics: computed(() => metrics.value),
    performanceGrade: getPerformanceGrade,

    // 核心方法
    startRenderTimer,
    endRenderTimer,
    updateCounts,
    updateMemoryUsage,
    startMonitoring,
    stopMonitoring,
    resetMetrics,

    // 分析方法
    generateReport,
    exportMetrics,
    measureFunction,
    runPerformanceTest,

    // 便捷方法
    logWarnings: logPerformanceWarnings
  };
}
