# GoJS 变电站图表组件

一个基于 GoJS 的高性能变电站网络图表组件，支持大规模数据渲染（600+ 节点）、实时数据更新、数据过滤和交互操作。

## 🚀 特性

- **高性能渲染**: 优化的模板和增量更新机制，支持 600+ 节点流畅渲染
- **实时数据更新**: 支持增量更新，避免全量重绘
- **数据过滤**: 按电压等级、地区等维度过滤显示
- **交互操作**: 节点搜索、缩放、拖拽、右键菜单
- **可配置显示**: 灵活的线路指标显示配置
- **TypeScript 支持**: 完整的类型定义

## 📦 组件结构

```
src/views/components/gojs/substation/
├── index.vue                    # 主组件
├── hooks/
│   ├── useGoJSDiagram.ts        # 图表核心逻辑
│   ├── useIncrementalUpdate.ts  # 增量更新逻辑
│   └── useLinkOperations.ts     # 连接线操作
├── components/
│   ├── NodeSearch.vue           # 节点搜索组件
│   ├── SubstationNodeEditor.vue # 节点编辑器
│   └── DataFilter.vue           # 数据过滤组件
├── ParallelRouteLink.ts         # 并行路由连接线
└── api.ts                       # API 接口
```

## 🔧 基本用法

```vue
<template>
  <SubstationDiagram
    :node="nodeData"
    :link="linkData"
    :loading="loading"
    @node-click="handleNodeClick"
    @node-double-click="handleNodeDoubleClick"
  />
</template>

<script setup>
import SubstationDiagram from '@/views/components/gojs/substation/index.vue'

const nodeData = ref([])
const linkData = ref([])
const loading = ref(false)

const handleNodeClick = (nodeData) => {
  console.log('节点点击:', nodeData)
}

const handleNodeDoubleClick = (nodeData) => {
  console.log('节点双击:', nodeData)
}
</script>
```

## 📊 数据格式

### 节点数据 (SubstationNode)

```typescript
interface SubstationNode {
  id?: string
  name: string
  x: number
  y: number
  voltage?: string        // 电压等级，如 "500kV"
  type?: 'station' | 'plant'
  zone?: string          // 地区信息
  properties?: {         // 扩展属性
    U?: string          // 电压值
    'U(M)'?: string     // 测量电压值
    [key: string]: any
  }
}
```

### 连接线数据 (SubstationLink)

```typescript
interface SubstationLink {
  name: string
  source: string         // 起始节点ID
  target: string         // 目标节点ID
  color?: string         // 线路颜色
  voltage?: string       // 电压等级
  direction?: 'forward' | 'backward'
  lineList?: string | Point[]  // 路径点
  properties?: {         // 扩展属性
    p_from_mw?: number   // 起点有功功率
    q_from_mvar?: number // 起点无功功率
    p_to_mw?: number     // 终点有功功率
    q_to_mvar?: number   // 终点无功功率
    loading_percent?: number // 负载率
    [key: string]: any
  }
}
```

## ⚡ 性能优化

### 1. 模板优化
- 使用 `shallowRef` 避免深度响应式
- 优化 GoJS 模板绑定，减少不必要的计算
- 合理使用 `pickable: false` 减少交互检测

### 2. 增量更新
```typescript
// 高频场景：只更新属性，不重新布局
updateNodeProperties(updates)
updateLinkProperties(updates)

// 中频场景：过滤显示，保持视图位置
showHideNodes(visibleNodeKeys)

// 低频场景：真正的增删节点
addRemoveNodes(nodeData, linkData)
```

### 3. 数据处理优化
- 使用 Map 结构优化数据查找
- 批量处理减少事务次数
- 合理的缓存策略

## 🎛️ 组件配置

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| node | SubstationNode[] | [] | 节点数据 |
| link | SubstationLink[] | [] | 连接线数据 |
| loading | boolean | false | 加载状态 |
| showToolbar | boolean | true | 显示工具栏 |
| showOverview | boolean | true | 显示缩略图 |
| enableLinkContextMenu | boolean | false | 启用连接线右键菜单 |

### Events

| 事件 | 参数 | 说明 |
|------|------|------|
| node-click | nodeData | 节点点击事件 |
| node-double-click | nodeData | 节点双击事件 |

### 插槽

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| link-context-menu | { linkData, closeMenu } | 自定义连接线右键菜单 |

## 🔍 高级功能

### 数据过滤

组件内置数据过滤功能，支持按电压等级和地区过滤：

```typescript
// 获取过滤状态
const { filterConditions, groupedData, filterStats } = diagramRef.value

// 重置过滤
diagramRef.value.resetFilter()

// 切换过滤面板
diagramRef.value.toggleFilterPanel()
```

### 连接线操作

```typescript
// 更新连接线颜色
diagramRef.value.updateLinkColor('line-key', '#ff0000')

// 批量更新连接线属性
diagramRef.value.updateMultipleLinksProperties([
  { linkKey: 'line1', properties: { color: '#ff0000' } },
  { linkKey: 'line2', properties: { color: '#00ff00' } }
])

// 条件更新
diagramRef.value.updateLinkColorByCondition(
  (linkData) => linkData.voltage === '500kV',
  '#ff0000'
)
```

### 节点搜索

内置节点搜索功能，支持模糊搜索和快速定位。

## 🐛 性能问题排查

### 常见性能问题

1. **初始渲染慢**
   - 检查数据量是否过大（建议 < 1000 节点）
   - 确认是否启用了不必要的动画
   - 检查模板复杂度

2. **更新卡顿**
   - 使用增量更新而非全量更新
   - 避免频繁的布局重计算
   - 检查是否有内存泄漏

3. **交互响应慢**
   - 减少 `pickable` 元素数量
   - 优化事件处理函数
   - 检查是否有阻塞主线程的操作

### 性能监控

```typescript
// 启用性能监控
console.time('diagram-render')
// ... 渲染操作
console.timeEnd('diagram-render')

// 监控内存使用
console.log('节点数量:', diagramRef.value.diagramInstance.nodes.count)
console.log('连接线数量:', diagramRef.value.diagramInstance.links.count)
```

## 📝 最佳实践

1. **数据预处理**: 在传入组件前完成数据清洗和格式化
2. **合理分页**: 超大数据集考虑分页或虚拟滚动
3. **缓存策略**: 合理使用缓存避免重复计算
4. **错误处理**: 添加完善的错误边界和降级方案
5. **类型安全**: 使用 TypeScript 确保数据类型正确

## 🔄 版本更新

### v2.0.0 (当前版本)
- ✅ 重构为 Composition API + Hooks 架构
- ✅ 优化性能，支持 600+ 节点
- ✅ 新增数据过滤功能
- ✅ 完善的 TypeScript 支持

### v1.x.x (已废弃)
- 基于 Options API 的旧版本
- 性能较差，不推荐使用

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交变更
4. 发起 Pull Request

## 📄 许可证

MIT License
