# API 文档

## 组件 Props

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `node` | `SubstationNode[]` | `[]` | 节点数据数组 |
| `link` | `SubstationLink[]` | `[]` | 连接线数据数组 |
| `loading` | `boolean` | `false` | 加载状态 |

### 显示控制

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `showToolbar` | `boolean` | `true` | 显示工具栏 |
| `showOverview` | `boolean` | `false` | 显示缩略图 |
| `showNodeInfo` | `boolean` | `true` | 显示节点信息 |
| `showConfigDialog` | `boolean` | `true` | 显示配置对话框 |
| `showEditNode` | `boolean` | `false` | 显示节点编辑器 |

### 交互控制

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enableLinkContextMenu` | `boolean` | `false` | 启用连接线右键菜单 |
| `enableNodeContextMenu` | `boolean` | `false` | 启用节点右键菜单 |
| `autoInitialize` | `boolean` | `false` | 自动初始化图表 |

### 过滤配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `filterConditions` | `FilterConditions` | `{ voltages: [], zones: [] }` | 初始过滤条件 |

## 组件 Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `node-click` | `(nodeData: SubstationNode)` | 节点点击事件 |
| `node-double-click` | `(nodeData: SubstationNode)` | 节点双击事件 |

## 组件 Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| `link-context-menu` | `{ linkData: SubstationLink, closeMenu: () => void }` | 自定义连接线右键菜单 |

## 组件方法 (通过 ref 访问)

### 图表控制

```typescript
// 初始化图表
initGraph(nodeData?: SubstationNode[], linkData?: SubstationLink[]): void

// 更新图表数据
updateGraphData(
  nodeData: SubstationNode[], 
  linkData: SubstationLink[], 
  options?: {
    incremental?: boolean
    forceLayout?: boolean
    preserveViewport?: boolean
  }
): void

// 销毁图表
destroyDiagram(): void
```

### 视图控制

```typescript
// 放大
zoomIn(): void

// 缩小
zoomOut(): void

// 重置视图
resetZoom(): void
```

### 数据过滤

```typescript
// 重置过滤条件
resetFilter(): void

// 切换过滤面板
toggleFilterPanel(): void

// 获取过滤统计
filterStats: {
  visibleNodes: number
  totalNodes: number
  visibleLinks: number
  totalLinks: number
  hasActiveFilter: boolean
}
```

### 连接线操作

```typescript
// 更新单个连接线颜色
updateLinkColor(linkKey: string, color: string): boolean

// 批量更新连接线颜色
updateMultipleLinkColors(updates: Array<{linkKey: string, color: string}>): boolean

// 条件更新连接线颜色
updateLinkColorByCondition(
  condition: (linkData: SubstationLink) => boolean, 
  color: string
): boolean

// 重置所有连接线颜色
resetAllLinkColors(defaultColor?: string): void

// 高亮连接线
highlightLinks(
  linkKeys: string[], 
  highlightColor?: string, 
  restoreOthers?: boolean
): boolean

// 更新连接线样式
updateLinkStyle(
  linkKey: string, 
  style: {
    color?: string
    strokeWidth?: number
    opacity?: number
    dashArray?: number[]
  }
): boolean
```

### 增量更新

```typescript
// 更新节点属性
updateNodeProperties(
  updates: Array<{
    nodeKey: string
    properties: Record<string, any>
  }>
): boolean

// 更新连接线属性
updateLinkProperties(
  updates: Array<{
    linkKey: string
    properties: Record<string, any>
  }>
): boolean

// 显示/隐藏节点
showHideNodes(
  allNodeData: SubstationNode[],
  visibleNodeKeys: string[]
): boolean
```

### 性能监控

```typescript
// 性能监控对象
performanceMonitor: {
  // 开始监控
  startMonitoring(): void
  
  // 停止监控
  stopMonitoring(): void
  
  // 重置指标
  resetMetrics(): void
  
  // 生成报告
  generateReport(): string
  
  // 导出指标
  exportMetrics(): PerformanceMetrics
  
  // 性能指标
  metrics: {
    renderTime: number
    nodeCount: number
    linkCount: number
    memoryUsage: number
    fps: number
    lastUpdateTime: number
  }
  
  // 性能评级
  performanceGrade: {
    grade: 'A' | 'B' | 'C' | 'D' | 'F'
    color: string
    text: string
  }
  
  // 监控状态
  isMonitoring: boolean
}
```

## 数据类型定义

### SubstationNode

```typescript
interface SubstationNode {
  id?: string                    // 节点唯一标识
  name: string                   // 节点名称
  x: number                      // X坐标
  y: number                      // Y坐标
  voltage?: string               // 电压等级 (如 "500kV")
  type?: 'station' | 'plant'     // 节点类型
  zone?: string                  // 地区信息
  color?: string                 // 节点颜色
  properties?: {                 // 扩展属性
    U?: string                   // 电压值
    'U(M)'?: string             // 测量电压值
    [key: string]: any          // 其他自定义属性
  }
}
```

### SubstationLink

```typescript
interface SubstationLink {
  name: string                   // 连接线名称
  source: string                 // 起始节点ID
  target: string                 // 目标节点ID
  color?: string                 // 连接线颜色
  voltage?: string               // 电压等级
  direction?: 'forward' | 'backward' // 方向
  lineList?: string | Point[]    // 路径点
  properties?: {                 // 扩展属性
    p_from_mw?: number          // 起点有功功率
    q_from_mvar?: number        // 起点无功功率
    p_to_mw?: number            // 终点有功功率
    q_to_mvar?: number          // 终点无功功率
    loading_percent?: number     // 负载率
    [key: string]: any          // 其他自定义属性
  }
}
```

### FilterConditions

```typescript
interface FilterConditions {
  voltages: string[]             // 选中的电压等级
  zones: string[]                // 选中的地区
}
```

### Point

```typescript
interface Point {
  x: number                      // X坐标
  y: number                      // Y坐标
}
```

## 配置选项

### LineIndicatorConfig

```typescript
interface LineIndicatorConfig {
  showLineName: boolean          // 显示线路名称
  showP: boolean                 // 显示有功功率
  showPQ: boolean                // 显示复功率
  showLoading: boolean           // 显示负载率
  showPM: boolean                // 显示测量有功功率
  showPQM: boolean               // 显示测量复功率
  showPDelta: boolean            // 显示有功功率差值
  showPQDelta: boolean           // 显示复功率差值
}
```

## 错误处理

### 常见错误

| 错误类型 | 说明 | 解决方案 |
|----------|------|----------|
| `图表实例未初始化` | 在图表初始化前调用方法 | 确保在 `onMounted` 后调用 |
| `数据格式错误` | 传入的数据格式不正确 | 检查数据类型定义 |
| `节点未找到` | 指定的节点不存在 | 检查节点ID是否正确 |
| `连接线未找到` | 指定的连接线不存在 | 检查连接线名称或ID |

### 错误监听

```typescript
// 监听组件错误
const handleError = (error: Error) => {
  console.error('图表组件错误:', error)
  // 错误处理逻辑
}

// 在组件中添加错误边界
onErrorCaptured((error) => {
  handleError(error)
  return false
})
```

## 性能建议

### 数据量建议

| 场景 | 节点数量 | 连接线数量 | 建议 |
|------|----------|------------|------|
| 小型 | < 100 | < 200 | 无特殊优化需求 |
| 中型 | 100-500 | 200-1000 | 启用增量更新 |
| 大型 | 500-1000 | 1000-2000 | 启用性能监控 |
| 超大型 | > 1000 | > 2000 | 考虑分页或虚拟化 |

### 优化配置

```typescript
// 大数据集优化配置
const optimizedConfig = {
  // 禁用动画
  animationEnabled: false,
  
  // 优化布局参数
  layout: {
    maxIterations: 150,
    springLength: 180,
    electricalCharge: 250
  },
  
  // 启用增量更新
  incrementalUpdate: true,
  
  // 启用性能监控
  performanceMonitoring: true
}
```
