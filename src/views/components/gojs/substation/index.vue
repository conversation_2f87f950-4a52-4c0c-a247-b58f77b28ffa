<template>
	<div class="w-full h-full relative rounded-md overflow-hidden" v-loading="loading" :element-loading-text="'加载厂站图中...'">
		<!-- 图表容器 -->
		<div ref="diagramRef" class="w-full h-full bg-white"></div>

		<!-- 工具栏 -->
		<div class="absolute top-0 left-0 right-0 z-10 bg-gray-50 p-2 border-b border-gray-200">
			<div class="flex items-center gap-2">
				<!-- 节点搜索组件 -->
				<NodeSearch :all-nodes="allNodes" :diagram="unref(diagramHook.diagramInstance)" />

				<!-- 工具按钮 -->
				<el-tooltip content="配置数据列" placement="top">
					<el-button class="!m-0" size="small" :icon="Setting" type="default" circle @click="openConfigDialog" />
				</el-tooltip>
				<el-tooltip content="放大" placement="top">
					<el-button class="!m-0" size="small" :icon="ZoomIn" type="default" circle @click="diagramHook.zoomIn" />
				</el-tooltip>
				<el-tooltip content="缩小" placement="top">
					<el-button class="!m-0" size="small" :icon="ZoomOut" type="default" circle @click="diagramHook.zoomOut" />
				</el-tooltip>
				<el-tooltip content="重置视图" placement="top">
					<el-button class="!m-0" size="small" :icon="FullScreen" type="default" circle @click="diagramHook.resetZoom" />
				</el-tooltip>
				<el-tooltip :content="showOverview ? '隐藏预览窗口' : '显示预览窗口'" placement="top">
					<el-button
						class="!m-0"
						:icon="showOverview ? Hide : View"
						size="small"
						:type="showOverview ? 'primary' : 'default'"
						circle
						@click="showOverview = !showOverview"
					/>
				</el-tooltip>
				<el-tooltip :content="showFilterPanel ? '隐藏过滤面板' : '显示过滤面板'" placement="top">
					<el-button class="!m-0" :icon="Filter" size="small" :type="showFilterPanel ? 'primary' : 'default'" circle @click="toggleFilterPanel" />
				</el-tooltip>
			</div>
		</div>

		<!-- 缩略图 -->
		<div
			v-show="showOverview"
			ref="overviewRef"
			class="absolute bottom-5 right-5 z-10 border border-gray-300 shadow-lg bg-white"
			style="width: 200px; height: 150px"
		/>

		<!-- 配置数据列弹窗 -->
		<el-dialog v-model="showConfigDialog" title="配置数据列" width="600px" destroy-on-close>
			<div class="space-y-6">
				<!-- 线路指标配置 -->
				<div>
					<h4 class="text-base font-medium mb-3 flex items-center">
						<el-icon class="mr-2"><Setting /></el-icon>
						线路指标
					</h4>
					<div class="grid grid-cols-2 gap-4">
						<el-checkbox v-model="diagramHook.lineIndicatorConfig.value.showLineName">线路名称</el-checkbox>
						<el-checkbox v-model="diagramHook.lineIndicatorConfig.value.showP">P</el-checkbox>
						<el-checkbox v-model="diagramHook.lineIndicatorConfig.value.showPQ">P+jQ</el-checkbox>
						<el-checkbox v-model="diagramHook.lineIndicatorConfig.value.showLoading">loading%</el-checkbox>
						<el-checkbox v-model="diagramHook.lineIndicatorConfig.value.showPM" disabled>P(M)</el-checkbox>
						<el-checkbox v-model="diagramHook.lineIndicatorConfig.value.showPQM" disabled>P+jQ(M)</el-checkbox>
						<el-checkbox v-model="diagramHook.lineIndicatorConfig.value.showPDelta" disabled>P(△)</el-checkbox>
						<el-checkbox v-model="diagramHook.lineIndicatorConfig.value.showPQDelta" disabled>P+jQ(△)</el-checkbox>
					</div>
					<p class="text-xs text-gray-500 mt-2">灰色选项表示数据暂未提供，后续将会支持</p>
				</div>
			</div>

			<template #footer>
				<div class="flex justify-end space-x-2">
					<el-button @click="showConfigDialog = false">取消</el-button>
					<el-button type="primary" @click="applyConfig">应用配置</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 节点编辑 -->
		<div v-if="showEditNode" class="substation-node-edit">
			<SubstationNodeEditor :diagram="unref(diagramHook.diagramInstance)" :selected-node-data="unref(diagramHook.selectedInfo).node" />
		</div>

		<!-- 右键菜单 -->
		<div
			v-if="showLinkContextMenu && contextMenuPosition && props.enableLinkContextMenu"
			class="fixed z-50 bg-white border border-gray-200 rounded-md shadow-lg py-1 min-w-32"
			:style="{
				left: contextMenuPosition.x + 'px',
				top: contextMenuPosition.y + 'px',
			}"
			@contextmenu.prevent
		>
			<slot name="link-context-menu" :link-data="contextMenuLinkData" :close-menu="closeLinkContextMenu">
				<!-- 默认菜单内容 -->
				<div class="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100">线路：{{ contextMenuLinkData?.name || '未知' }}</div>
			</slot>
		</div>

		<!-- 右键菜单遮罩 - 点击其他地方关闭菜单 -->
		<div
			v-if="showLinkContextMenu && props.enableLinkContextMenu"
			class="fixed inset-0 z-40"
			@click="closeLinkContextMenu"
			@contextmenu.prevent="closeLinkContextMenu"
		></div>

		<!-- 数据过滤面板 -->
		<DataFilter
			:visible="showFilterPanel"
			:original-node-data="originalNodeData"
			:show-stats="true"
			@close="toggleFilterPanel"
			@filter="applyFilter"
			v-model:filter-conditions="filterConditions"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, watch, unref, onBeforeUnmount, readonly } from 'vue';
import * as go from 'gojs';
import { View, Hide, ZoomIn, ZoomOut, FullScreen, Setting, Filter } from '@element-plus/icons-vue';
import NodeSearch from './components/NodeSearch.vue';
import SubstationNodeEditor from './components/SubstationNodeEditor.vue';
import DataFilter from './components/DataFilter.vue';
import { useGoJSDiagram } from './hooks/useGoJSDiagram';
import { useIncrementalUpdate } from './hooks/useIncrementalUpdate';
import { useLinkOperations } from './hooks/useLinkOperations';
import { usePerformanceMonitor } from './hooks/usePerformanceMonitor';

// ===== 组件默认参数 =====
const props = withDefaults(
	defineProps<{
		showToolbar?: boolean;
		showOverview?: boolean;
		showNodeInfo?: boolean;
		showConfigDialog?: boolean;
		showEditNode?: boolean;
		enableLinkContextMenu?: boolean;
		enableNodeContextMenu?: boolean;
		autoInitialize?: boolean;
		filterConditions?: {
			voltages?: string[];
			zones?: string[];
		};
	}>(),
	{
		showToolbar: true,
		showOverview: false,
		showNodeInfo: true,
		showConfigDialog: true,
		showEditNode: false,
		enableLinkContextMenu: false,
		enableNodeContextMenu: false,
		autoInitialize: false,
		filterConditions: () => ({
			voltages: [],
			zones: [],
		}),
	}
);
const filterConditions = ref<{ voltages: string[]; zones: string[] }>({
	voltages: props.filterConditions?.voltages || [],
	zones: props.filterConditions?.zones || [],
});

const emit = defineEmits(['node-click', 'node-double-click']);

// ===== 基础状态管理 =====
const loading = defineModel<boolean>('loading', { default: true });
const showOverview = ref<boolean>(props.showOverview);
const showConfigDialog = ref<boolean>(false);
const isGraphInitialized = ref<boolean>(false);

// ===== DOM引用 =====
const diagramRef = ref<HTMLElement | null>(null);
const overviewRef = ref<HTMLElement | null>(null);

// ===== 节点数据状态 =====
const allNodes = ref<SubstationNode[]>([]);

// ===== 右键菜单状态 =====
const showLinkContextMenu = ref<boolean>(false);
const contextMenuPosition = ref<{ x: number; y: number } | null>(null);
const contextMenuLinkData = ref<SubstationLink | null>(null);

// ===== 使用hooks =====
const diagramHook = useGoJSDiagram();
const incrementalHook = useIncrementalUpdate();
const linkOpsHook = useLinkOperations();
const performanceMonitor = usePerformanceMonitor();

// ===== 数据过滤相关状态 =====
const showFilterPanel = ref<boolean>(false);
const originalNodeData = ref<SubstationNode[]>([]);
const originalLinkData = ref<SubstationLink[]>([]);

/**
 * 切换过滤面板显示状态
 */
const toggleFilterPanel = (): void => {
	showFilterPanel.value = !showFilterPanel.value;
};

/**
 * 应用过滤：显示/隐藏节点
 */
const applyFilter = (filteredNodes: SubstationNode[]): void => {
	const myDiagram = diagramHook._getDiagram();
	if (!myDiagram || !diagramHook.isInitialized.value || originalNodeData.value.length === 0) {
		console.warn('图表实例未初始化或无原始数据，无法应用过滤');
		return;
	}

	// 获取过滤后节点的 keys
	const visibleNodeKeys = filteredNodes.map((node) => incrementalHook.getNodeKey(node));

	// 使用 showHideNodes 方法应用过滤
	const success = incrementalHook.showHideNodes(
		myDiagram,
		originalNodeData.value,
		visibleNodeKeys,
		diagramHook.convertToLinkData,
		originalLinkData.value
	);

	if (success) {
		console.log(`过滤应用成功: 显示 ${filteredNodes.length}/${originalNodeData.value.length} 个节点`);
	} else {
		console.error('过滤应用失败');
	}
};

// ===== 销毁函数 =====
const destroyDiagram = () => {
	diagramHook.destroyDiagram();
	incrementalHook.clearCache();

	// 清理状态
	isGraphInitialized.value = false;
	originalNodeData.value = [];
	originalLinkData.value = [];
	showFilterPanel.value = false;
	allNodes.value = [];

	console.log('组件资源已清理');
};

// ===== 右键菜单方法 =====

/**
 * 关闭右键菜单
 */
const closeLinkContextMenu = () => {
	showLinkContextMenu.value = false;
	contextMenuPosition.value = null;
	contextMenuLinkData.value = null;
};

/**
 * 显示右键菜单
 */
const showLinkContextMenuAt = (x: number, y: number, linkData: SubstationLink) => {
	// 检查是否启用了右键菜单
	if (!props.enableLinkContextMenu) {
		return;
	}
	contextMenuPosition.value = { x, y };
	contextMenuLinkData.value = linkData;
	showLinkContextMenu.value = true;
};

// ===== 工具方法 =====

/**
 * 统一的加载状态管理
 */
const setLoading = (state: boolean, delay: number = 0) => {
	if (delay > 0) {
		setTimeout(() => {
			loading.value = state;
		}, delay);
	} else {
		loading.value = state;
	}
};

/**
 * 居中视图方法（提取公共逻辑）
 */
const centerView = (diagram: go.Diagram, autoCenter: boolean = true) => {
	if (!autoCenter) return;

	setTimeout(() => {
		if (diagram) {
			const docBounds = diagram.documentBounds;
			if (docBounds.width > 0 && docBounds.height > 0) {
				diagram.centerRect(docBounds);
			} else {
				diagram.scrollToRect(docBounds);
			}
		}
	}, 0);
};

// ===== 图表操作方法（按使用频率和场景分类）=====
//
// 使用指南：
// 场景0: initGraph() - 传统完整初始化（向后兼容）
// 场景1: initBlankGraph() - 快速预初始化空白图表
// 场景2: loadGraphData() - 异步数据加载到已初始化图表
// 场景3: updateGraphData() - 完整数据更新（支持增量选项）
//
// 性能优化建议：
// 1. 页面加载使用 autoInitialize + loadGraphData
// 2. 实时更新使用 updateNodes/updateLinks
// 3. 数据刷新使用 updateGraphData + incremental选项
// =================================================================

/**
 * 场景1：初始化空白图表实例
 * 用途：页面加载时快速显示图表容器，提升用户体验
 * 适用：预初始化、快速响应场景
 */
const initBlankGraph = (): boolean => {
	if (!diagramRef.value) {
		console.warn('图表容器未找到，无法初始化');
		return false;
	}

	// 初始化图表实例
	if (!diagramHook.isInitialized.value) {
		diagramHook.initDiagram(diagramRef.value, {
			enableLinkContextMenu: props.enableLinkContextMenu,
			emit: emit as (event: string, ...args: any[]) => void,
			showLinkContextMenuAt,
		});
	}

	const myDiagram = diagramHook._getDiagram();
	if (!myDiagram || !diagramHook.isInitialized.value) {
		console.warn('图表实例初始化失败');
		return false;
	}

	try {
		// 创建空的数据模型
		const emptyModel = new go.GraphLinksModel([], []);
		myDiagram.model = emptyModel;

		isGraphInitialized.value = true;

		console.log('空白图表初始化完成');
		return true;
	} catch (error) {
		console.error('初始化空白图表失败:', error);
		return false;
	}
};

/**
 * 场景2：加载数据到已初始化的图表中
 * 用途：向已预初始化的空白图表加载数据，性能优化场景
 * 适用：异步数据加载、预初始化后的数据更新
 */
const loadGraphData = (
	nodeData: SubstationNode[],
	linkData: SubstationLink[],
	options: {
		autoCenter?: boolean;
		preserveViewport?: boolean;
	} = {}
): boolean => {
	const { autoCenter = true, preserveViewport = false } = options;

	// 如果图表未初始化，先初始化空白图表
	if (!isGraphInitialized.value) {
		const success = initBlankGraph();
		if (!success) {
			return false;
		}
	}

	const myDiagram = diagramHook._getDiagram();
	if (!myDiagram || !diagramHook.isInitialized.value) {
		console.warn('图表实例未准备好，无法加载数据');
		return false;
	}

	try {
		setLoading(true);

		// 更新搜索节点数据和原始数据
		allNodes.value = nodeData;
		originalNodeData.value = [...nodeData];
		originalLinkData.value = [...linkData];

		// 转换连接线数据并更新模型
		const convertedLinks = diagramHook.convertToLinkData(linkData);
		const newModel = new go.GraphLinksModel(nodeData, convertedLinks);
		myDiagram.model = newModel;

		// 触发布局计算
		myDiagram.layoutDiagram(true);

		// 统一的视图居中处理
		const shouldCenter =
			autoCenter && !preserveViewport && props.filterConditions?.voltages?.length === 0 && props.filterConditions?.zones?.length === 0;

		if (shouldCenter) {
			centerView(myDiagram);
		}

		// 更新缓存
		incrementalHook.cachedNodeData.value = [...nodeData];
		incrementalHook.cachedLinkData.value = [...linkData];

		console.log('图表数据加载完成');
		return true;
	} catch (error) {
		console.error('加载图表数据失败:', error);
		return false;
	} finally {
		setLoading(false, 100);
	}
};

/**
 * 场景0：完整初始化图表（向后兼容的统一入口）
 * 用途：传统的一次性初始化，兼容现有代码
 * 适用：有完整数据时的直接初始化
 */
const initGraph = (nodeData?: SubstationNode[], linkData?: SubstationLink[]): boolean => {
	// 如果没有传入数据，则只初始化空白图表
	if (!nodeData || !linkData || (nodeData.length === 0 && linkData.length === 0)) {
		console.log('无数据，初始化空白图表');
		return initBlankGraph();
	}

	// 有数据时，先初始化空白图表再加载数据
	const blankSuccess = initBlankGraph();
	if (!blankSuccess) {
		return false;
	}

	// 使用优化的数据加载方法
	return loadGraphData(nodeData, linkData, { autoCenter: true });
};

/**
 * 场景3：完整更新图表数据（支持增量更新选项）
 * 用途：数据变更、过滤、排序等需要完整重建的场景
 * 适用：数据刷新、视图模式切换、布局重新计算
 */
function updateGraphData(
	nodeData: SubstationNode[],
	linkData: SubstationLink[],
	options: {
		incremental?: boolean; // 是否使用增量更新
		forceLayout?: boolean; // 是否强制重新布局
		preserveViewport?: boolean; // 是否保持当前视图位置
		analyzeData?: boolean; // 是否重新分析数据（用于过滤等）
	} = {}
) {
	const myDiagram = diagramHook._getDiagram();
	if (!myDiagram || !diagramHook.isInitialized.value) {
		console.warn('图表实例未初始化，无法更新数据');
		return;
	}

	const { incremental = false, forceLayout = true, preserveViewport = false, analyzeData = true } = options;

	// 优先使用增量更新（性能优化）
	if (incremental) {
		const success = incrementalHook.addRemoveNodes(myDiagram, nodeData, linkData, diagramHook.convertToLinkData, {
			forceLayout,
			preserveViewport,
		});

		if (success) {
			setLoading(false, 100);
			return;
		} else {
			console.warn('增量更新失败，回退到完整更新模式');
		}
	}

	try {
		setLoading(true);

		// 开始性能监控
		performanceMonitor.startRenderTimer();

		// 更新组件状态
		allNodes.value = nodeData;

		if (analyzeData) {
			originalNodeData.value = [...nodeData];
			originalLinkData.value = [...linkData];
		}

		// 更新图表模型
		const links = diagramHook.convertToLinkData(linkData);
		const newModel = new go.GraphLinksModel(nodeData, links);
		myDiagram.model = newModel;

		// 更新性能监控数据
		performanceMonitor.updateCounts(nodeData.length, links.length);

		// 布局处理
		if (forceLayout) {
			myDiagram.layoutDiagram(true);
		}

		// 视图处理
		if (!preserveViewport) {
			myDiagram.scale = 1;
			centerView(myDiagram);
		}

		// 更新缓存
		incrementalHook.cachedNodeData.value = [...nodeData];
		incrementalHook.cachedLinkData.value = [...linkData];

		console.log('图表数据更新完成');
	} catch (error) {
		console.error('更新图表数据失败:', error);
	} finally {
		// 结束性能监控
		performanceMonitor.endRenderTimer();
		setLoading(false, preserveViewport ? 100 : 200);
	}
}

// ===== 配置管理 =====
const openConfigDialog = () => {
	showConfigDialog.value = true;
};

const applyConfig = () => {
	showConfigDialog.value = false;
	diagramHook.applyConfig();
};

// ===== 其他监听器 =====

watch(showOverview, (newValue) => {
	if (newValue && overviewRef.value) {
		setTimeout(() => {
			if (overviewRef.value) {
				diagramHook.initOverview(overviewRef.value);
			}
		}, 100);
	}
});

watch(
	diagramHook.lineIndicatorConfig,
	() => {
		const myDiagram = diagramHook._getDiagram();
		if (myDiagram && diagramHook.isInitialized.value) {
			myDiagram.requestUpdate();
		}
	},
	{ deep: true }
);

// ===== 组件生命周期 =====

// 组件挂载时的自动初始化逻辑
watch(
	() => diagramRef.value,
	(newRef) => {
		if (newRef && props.autoInitialize && !isGraphInitialized.value) {
			console.log('自动初始化空白图表');
			initBlankGraph();
		}
	},
	{ immediate: true }
);

onBeforeUnmount(() => {
	// 组件销毁前清理资源
	destroyDiagram();
});

// ===== 组件接口 =====
defineExpose({
	diagram: diagramHook.diagramInstance,
	initGraph,
	initBlankGraph, // 场景1：初始化空白图表
	loadGraphData, // 场景2：加载数据到已初始化的图表
	updateGraphData, // 场景3：完整更新图表数据
	destroyDiagram,
	isInitialized: diagramHook.isInitialized,
	isGraphInitialized, // 图表是否已初始化状态

	// 增量更新方法（按使用频率分类）
	// 高频场景：实时数据更新
	updateNodeProperties: incrementalHook.updateNodeProperties,
	updateLinkProperties: incrementalHook.updateLinkProperties,
	updateNodes: incrementalHook.updateNodes, // 推荐：统一节点更新接口
	updateLinks: incrementalHook.updateLinks, // 推荐：统一连接线更新接口

	// 中频场景：条件批量更新、过滤显示
	updateNodesByCondition: incrementalHook.updateNodesByCondition,
	updateLinksByCondition: incrementalHook.updateLinksByCondition,
	showHideNodes: incrementalHook.showHideNodes,

	// 低频场景：结构性变更
	addRemoveNodes: incrementalHook.addRemoveNodes,

	// 向后兼容（已废弃，请使用新方法）
	updateSingleNodeProperties: incrementalHook.updateSingleNodeProperties,
	updateSingleLinkProperties: incrementalHook.updateSingleLinkProperties,
	updateNodePropertiesByCondition: incrementalHook.updateNodePropertiesByCondition,
	updateLinkPropertiesByCondition: incrementalHook.updateLinkPropertiesByCondition,

	// 数据过滤相关方法
	filterConditions,
	toggleFilterPanel,
	applyFilter,

	// 过滤状态
	showFilterPanel: readonly(showFilterPanel),
	originalNodeData: readonly(originalNodeData),

	// 线路操作方法 (通过hook提供)
	updateLinkProperty: (linkKey: string, propertyName: string, propertyValue: any, transactionName?: string, updateDataModel?: boolean) =>
		linkOpsHook.updateLinkProperty(diagramHook._getDiagram(), linkKey, propertyName, propertyValue, transactionName, updateDataModel),
	updateLinkPropertiesOld: (linkKey: string, properties: Record<string, any>, transactionName?: string, updateDataModel?: boolean) =>
		linkOpsHook.updateLinkProperties(diagramHook._getDiagram(), linkKey, properties, transactionName, updateDataModel),
	updateMultipleLinksProperties: (
		updates: { linkKey: string; properties: Record<string, any> }[],
		transactionName?: string,
		updateDataModel?: boolean
	) => linkOpsHook.updateMultipleLinksProperties(diagramHook._getDiagram(), updates, transactionName, updateDataModel),
	updateLinkPropertiesByConditionOld: (
		condition: (linkData: any) => boolean,
		properties: Record<string, any>,
		transactionName?: string,
		updateDataModel?: boolean
	) => linkOpsHook.updateLinkPropertiesByCondition(diagramHook._getDiagram(), condition, properties, transactionName, updateDataModel),

	// 便捷颜色控制方法
	updateLinkColor: (linkKey: string, color: string) => linkOpsHook.updateLinkColor(diagramHook._getDiagram(), linkKey, color),
	updateMultipleLinkColors: (updates: { linkKey: string; color: string }[]) =>
		linkOpsHook.updateMultipleLinkColors(diagramHook._getDiagram(), updates),
	updateLinkColorByCondition: (condition: (linkData: any) => boolean, color: string) =>
		linkOpsHook.updateLinkColorByCondition(diagramHook._getDiagram(), condition, color),
	resetAllLinkColors: (defaultColor?: string) => linkOpsHook.resetAllLinkColors(diagramHook._getDiagram(), defaultColor),
	highlightLinks: (linkKeys: string[], highlightColor?: string, restoreOthers?: boolean) =>
		linkOpsHook.highlightLinks(diagramHook._getDiagram(), linkKeys, highlightColor, restoreOthers),

	// 更多便捷方法
	updateLinkStrokeWidth: (linkKey: string, strokeWidth: number) => linkOpsHook.updateLinkStrokeWidth(diagramHook._getDiagram(), linkKey, strokeWidth),
	updateLinkOpacity: (linkKey: string, opacity: number) => linkOpsHook.updateLinkOpacity(diagramHook._getDiagram(), linkKey, opacity),
	updateLinkStyle: (linkKey: string, style: { color?: string; strokeWidth?: number; opacity?: number; dashArray?: number[] }) =>
		linkOpsHook.updateLinkStyle(diagramHook._getDiagram(), linkKey, style),

	// 线路数据获取方法
	getLinkData: (linkKey: string) => linkOpsHook.getLinkData(diagramHook._getDiagram(), linkKey),
	getAllLinkData: () => linkOpsHook.getAllLinkData(diagramHook._getDiagram()),

	// 视图控制方法
	zoomIn: diagramHook.zoomIn,
	zoomOut: diagramHook.zoomOut,
	resetZoom: diagramHook.resetZoom,

	// 右键菜单控制方法
	closeLinkContextMenu,
	showLinkContextMenuAt,

	// 性能监控方法
	performanceMonitor: {
		startMonitoring: performanceMonitor.startMonitoring,
		stopMonitoring: performanceMonitor.stopMonitoring,
		resetMetrics: performanceMonitor.resetMetrics,
		generateReport: performanceMonitor.generateReport,
		exportMetrics: performanceMonitor.exportMetrics,
		metrics: performanceMonitor.metrics,
		performanceGrade: performanceMonitor.performanceGrade,
		isMonitoring: performanceMonitor.isMonitoring,
	},
});
</script>
