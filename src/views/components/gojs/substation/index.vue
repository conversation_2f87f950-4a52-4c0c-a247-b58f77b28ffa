<template>
	<div class="w-full h-full relative rounded-md" v-loading="loading" :element-loading-text="'加载厂站图中...'">
		<!-- 图表容器 -->
		<div ref="diagramRef" class="w-full h-full bg-white"></div>

		<!-- 工具栏 -->
		<div class="absolute top-0 left-0 right-0 z-10 bg-gray-50 p-2 border-b border-gray-200">
			<div class="flex items-center gap-2">
				<!-- 节点搜索组件 -->
				<NodeSearch :all-nodes="allNodes" :diagram="diagramInstance" @node-selected="handleSearchNodeSelected" />

				<!-- 工具按钮 -->
				<el-tooltip content="配置数据列" placement="top">
					<el-button class="!m-0" size="small" :icon="Setting" type="default" circle @click="openConfigDialog" />
				</el-tooltip>
				<el-tooltip content="放大" placement="top">
					<el-button class="!m-0" size="small" :icon="ZoomIn" type="default" circle @click="zoomIn" />
				</el-tooltip>
				<el-tooltip content="缩小" placement="top">
					<el-button class="!m-0" size="small" :icon="ZoomOut" type="default" circle @click="zoomOut" />
				</el-tooltip>
				<el-tooltip content="重置视图" placement="top">
					<el-button class="!m-0" size="small" :icon="FullScreen" type="default" circle @click="resetZoom" />
				</el-tooltip>
				<el-tooltip :content="showOverview ? '隐藏预览窗口' : '显示预览窗口'" placement="top">
					<el-button
						class="!m-0"
						:icon="showOverview ? Hide : View"
						size="small"
						:type="showOverview ? 'primary' : 'default'"
						circle
						@click="showOverview = !showOverview"
					/>
				</el-tooltip>
				<el-tooltip :content="showFilterPanel ? '隐藏过滤面板' : '显示过滤面板'" placement="top">
					<el-button
						class="!m-0"
						:icon="showFilterPanel ? Hide : View"
						size="small"
						:type="showFilterPanel ? 'primary' : 'default'"
						circle
						@click="toggleFilterPanel"
					/>
				</el-tooltip>
			</div>
		</div>

		<!-- 缩略图 -->
		<div
			v-show="showOverview"
			ref="overviewRef"
			class="absolute bottom-5 right-5 z-10 border border-gray-300 shadow-lg bg-white"
			style="width: 200px; height: 150px"
		/>

		<!-- 配置数据列弹窗 -->
		<el-dialog v-model="showConfigDialog" title="配置数据列" width="600px" destroy-on-close>
			<div class="space-y-6">
				<!-- 线路指标配置 -->
				<div>
					<h4 class="text-base font-medium mb-3 flex items-center">
						<el-icon class="mr-2"><Setting /></el-icon>
						线路指标
					</h4>
					<div class="grid grid-cols-2 gap-4">
						<el-checkbox v-model="lineIndicatorConfig.showLineName">线路名称</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showP">P</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPQ">P+jQ</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showLoading">loading%</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPM" disabled>P(M)</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPQM" disabled>P+jQ(M)</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPDelta" disabled>P(△)</el-checkbox>
						<el-checkbox v-model="lineIndicatorConfig.showPQDelta" disabled>P+jQ(△)</el-checkbox>
					</div>
					<p class="text-xs text-gray-500 mt-2">灰色选项表示数据暂未提供，后续将会支持</p>
				</div>
			</div>

			<template #footer>
				<div class="flex justify-end space-x-2">
					<el-button @click="showConfigDialog = false">取消</el-button>
					<el-button type="primary" @click="applyConfig">应用配置</el-button>
				</div>
			</template>
		</el-dialog>

		<!-- 节点编辑 -->
		<div v-if="showEditNode" class="substation-node-edit">
			<SubstationNodeEditor :diagram="diagramInstance" :selected-node-data="selectedNodeData" />
		</div>

		<!-- 右键菜单 -->
		<div
			v-if="showLinkContextMenu && contextMenuPosition && props.enableLinkContextMenu"
			class="fixed z-50 bg-white border border-gray-200 rounded-md shadow-lg py-1 min-w-32"
			:style="{
				left: contextMenuPosition.x + 'px',
				top: contextMenuPosition.y + 'px',
			}"
			@contextmenu.prevent
		>
			<slot name="link-context-menu" :link-data="contextMenuLinkData" :close-menu="closeLinkContextMenu">
				<!-- 默认菜单内容 -->
				<div class="px-3 py-2 text-sm cursor-pointer hover:bg-gray-100">线路：{{ contextMenuLinkData?.name || '未知' }}</div>
			</slot>
		</div>

		<!-- 右键菜单遮罩 - 点击其他地方关闭菜单 -->
		<div
			v-if="showLinkContextMenu && props.enableLinkContextMenu"
			class="fixed inset-0 z-40"
			@click="closeLinkContextMenu"
			@contextmenu.prevent="closeLinkContextMenu"
		></div>

		<!-- 数据过滤面板 -->
		<div
			v-show="showFilterPanel"
			class="absolute bottom-5 right-5 z-20 bg-white border border-gray-300 rounded-lg shadow-lg"
			style="width: 320px; max-height: 600px"
		>
			<!-- 面板头部 -->
			<div class="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
				<h4 class="text-sm font-medium text-gray-900 flex items-center">
					<el-icon class="mr-2"><Setting /></el-icon>
					数据过滤
				</h4>
				<div class="flex items-center space-x-2">
					<el-button size="small" type="text" @click="resetFilter">重置</el-button>
					<el-button size="small" type="text" @click="showFilterPanel = false">
						<el-icon><Hide /></el-icon>
					</el-button>
				</div>
			</div>

			<!-- 面板内容 -->
			<div class="p-3 space-y-4 overflow-y-auto">
				<!-- 电压等级过滤 -->
				<div v-if="groupedData.voltages.length > 0">
					<div class="flex items-center justify-between mb-2">
						<label class="text-sm font-medium text-gray-700">电压等级</label>
						<!-- <div class="flex space-x-1">
							<el-button size="small" type="text" @click="filterConditions.voltages = groupedData.voltages.map((v) => v.value)"> 全选 </el-button>
							<el-button size="small" type="text" @click="filterConditions.voltages = []"> 清空 </el-button>
						</div> -->
					</div>
					<div class="space-y-1 max-h-50 overflow-y-auto">
						<el-checkbox
							v-for="voltage in groupedData.voltages"
							:key="voltage.value"
							v-model="filterConditions.voltages"
							:label="voltage.value"
							class="!mr-0 w-full"
						>
							<span class="flex justify-between items-center w-full">
								<span class="text-sm">{{ voltage.label }}</span>
								<span class="text-xs text-gray-500 bg-gray-100 px-1 rounded">{{ voltage.count }}</span>
							</span>
						</el-checkbox>
					</div>
				</div>

				<!-- 地区过滤 -->
				<div v-if="groupedData.zones.length > 0">
					<div class="flex items-center justify-between mb-2">
						<label class="text-sm font-medium text-gray-700">地区</label>
						<!-- <div class="flex space-x-1">
							<el-button size="small" type="text" @click="filterConditions.zones = groupedData.zones.map((z) => z.value)"> 全选 </el-button>
							<el-button size="small" type="text" @click="filterConditions.zones = []"> 清空 </el-button>
						</div> -->
					</div>
					<div class="space-y-1 max-h-50 overflow-y-auto">
						<el-checkbox
							v-for="zone in groupedData.zones"
							:key="zone.value"
							v-model="filterConditions.zones"
							:label="zone.value"
							class="!mr-0 w-full"
							style="--el-checkbox-height: 14px"
						>
							<span class="flex justify-between items-center w-full">
								<span class="text-sm">{{ zone.label }}</span>
								<span class="text-xs text-gray-500 bg-gray-100 px-1 rounded">{{ zone.count }}</span>
							</span>
						</el-checkbox>
					</div>
				</div>

				<!-- 统计信息 -->
				<div class="pt-2 border-t border-gray-200">
					<div class="text-xs text-gray-600 space-y-1">
						<div class="flex justify-between">
							<span>显示节点:</span>
							<span>{{ filterStats.visibleNodes }} / {{ filterStats.totalNodes }}</span>
						</div>
						<!-- <div class="flex justify-between">
							<span>显示连接线:</span>
							<span>{{ filterStats.visibleLinks }} / {{ filterStats.totalLinks }}</span>
						</div> -->
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, shallowRef, watch, watchEffect, onMounted, onBeforeUnmount, nextTick, inject, ComputedRef, readonly, computed } from 'vue';
import * as go from 'gojs';
import { View, Hide, ZoomIn, ZoomOut, FullScreen, Setting } from '@element-plus/icons-vue';
import NodeSearch from './components/NodeSearch.vue';
import SubstationNodeEditor from './components/SubstationNodeEditor.vue';
import { ParallelRouteLink } from './ParallelRouteLink';
import { COLORS, ZOOM, getLineSegmentFraction, getInnerCircles } from '/@/config/GraphConfig';

// ===== 组件默认参数 =====
const props = withDefaults(
	defineProps<{
		node: SubstationNode[];
		link: SubstationLink[];
		showToolbar?: boolean;
		showOverview?: boolean;
		showNodeInfo?: boolean;
		showConfigDialog?: boolean;
		showEditNode?: boolean;
		enableLinkContextMenu?: boolean;
		enableNodeContextMenu?: boolean;
	}>(),
	{
		node: () => [],
		link: () => [],
		showToolbar: true,
		showOverview: true,
		showNodeInfo: true,
		showConfigDialog: true,
		showEditNode: false,
		enableLinkContextMenu: false,
		enableNodeContextMenu: false,
	}
);

const emit = defineEmits(['node-click', 'node-double-click']);

// ===== 状态管理 =====
const loading = defineModel<boolean>('loading', { default: true });

const showOverview = ref<boolean>(false);
const showConfigDialog = ref<boolean>(false);
const isInitialized = ref<boolean>(false);

// ===== 右键菜单状态 =====
const showLinkContextMenu = ref<boolean>(false);
const contextMenuPosition = ref<{ x: number; y: number } | null>(null);
const contextMenuLinkData = ref<SubstationLink | null>(null);

// ===== DOM引用 =====
const diagramRef = ref<HTMLElement | null>(null);
const overviewRef = ref<HTMLElement | null>(null);

// ===== GoJS实例 =====
let myDiagram: go.Diagram | null = null;
const diagramInstance = shallowRef<go.Diagram | null>(null);
let myOverview: go.Overview | null = null;
let $: any = null;

// ===== 节点数据状态 =====
const allNodes = ref<SubstationNode[]>([]);
const selectedNodeData = ref<SubstationNode | null>(null);

// ===== 增量更新缓存状态 =====
const cachedNodeData = ref<SubstationNode[]>([]);
const cachedLinkData = ref<SubstationLink[]>([]);

// ===== 线路指标配置 =====
const lineIndicatorConfig = ref<LineIndicatorConfig>({
	showLineName: true,
	showP: false,
	showPQ: false,
	showLoading: true,
	showPM: false,
	showPQM: false,
	showPDelta: false,
	showPQDelta: false,
});

// ===== 数据过滤相关状态 =====
const showFilterPanel = ref<boolean>(false);
const originalNodeData = ref<SubstationNode[]>([]);
const originalLinkData = ref<SubstationLink[]>([]);

// 过滤条件状态
const filterConditions = ref<{
	voltages: string[];
	zones: string[];
}>({
	voltages: [],
	zones: [],
});

// 分组数据
const groupedData = ref<{
	voltages: { value: string; label: string; count: number; nodes: SubstationNode[] }[];
	zones: { value: string; label: string; count: number; nodes: SubstationNode[] }[];
}>({
	voltages: [],
	zones: [],
});

// ===== 计算属性：过滤后的数据 =====

/**
 * 计算属性：过滤后的节点数据
 * 基于原始数据和过滤条件自动计算，内置缓存机制
 */
const filteredNodeData = computed<SubstationNode[]>(() => {
	if (!originalNodeData.value.length) {
		return [];
	}

	const conditions = filterConditions.value;

	// 如果没有任何过滤条件，返回所有原始数据
	if (conditions.voltages.length === 0 && conditions.zones.length === 0) {
		return [...originalNodeData.value];
	}

	// 基于原始数据进行过滤
	return originalNodeData.value.filter((node) => {
		// 电压过滤：如果没有选择任何电压，显示所有；如果有选择，只显示选中的
		const voltageMatch = conditions.voltages.length === 0 || conditions.voltages.includes(node.voltage || '未知电压');

		// 地区过滤：如果没有选择任何地区，显示所有；如果有选择，只显示选中的
		const zoneName = (node as any).zone || '未知地区';
		const zoneMatch = conditions.zones.length === 0 || conditions.zones.includes(zoneName);

		return voltageMatch && zoneMatch;
	});
});

/**
 * 计算属性：过滤后的连接线数据
 * 保持所有原始连接线，不做额外过滤
 */
const filteredLinkData = computed<SubstationLink[]>(() => {
	// 直接返回所有原始连接线数据，不进行过滤
	return [...originalLinkData.value];
});

/**
 * 计算属性：过滤统计信息
 */
const filterStats = computed(() => ({
	visibleNodes: filteredNodeData.value.length,
	totalNodes: originalNodeData.value.length,
	visibleLinks: filteredLinkData.value.length,
	totalLinks: originalLinkData.value.length,
	hasActiveFilter: filterConditions.value.voltages.length > 0 || filterConditions.value.zones.length > 0,
}));

// ===== 资源清理和销毁 =====

/**
 * 销毁图表实例并清理资源
 */
const destroyDiagram = () => {
	try {
		// 清理缩略图
		if (myOverview) {
			myOverview.div = null;
			myOverview = null;
		}

		// 清理主图表
		if (myDiagram) {
			myDiagram.div = null;
			myDiagram = null;
		}

		// 重置状态
		diagramInstance.value = null;
		isInitialized.value = false;
		$ = null;

		// 清理缓存
		cachedNodeData.value = [];
		cachedLinkData.value = [];

		// 清理过滤状态
		originalNodeData.value = [];
		originalLinkData.value = [];
		// filteredNodeData 和 filteredLinkData 现在是计算属性，会自动更新
		filterConditions.value.voltages = [];
		filterConditions.value.zones = [];
		groupedData.value.voltages = [];
		groupedData.value.zones = [];
		showFilterPanel.value = false;

		console.log('图表实例已清理');
	} catch (error) {
		console.error('清理图表实例时发生错误:', error);
	}
};

// ===== 格式化函数 =====
/**
 * 格式化复数显示
 */
const formatComplexNumber = (realPart: number, imagPart: number): string => {
	if (imagPart >= 0) {
		return `${realPart.toFixed(2)}+j${imagPart.toFixed(2)}`;
	} else {
		return `${realPart.toFixed(2)}-j${Math.abs(imagPart).toFixed(2)}`;
	}
};

/**
 * 格式化线路数据显示
 */
const formatLineDataSingle = (data: SubstationLink): string => {
	const config = lineIndicatorConfig.value;
	const props: any = data.properties || {};

	// From端数据
	const fromParts: string[] = [];
	if (config.showP) {
		const pFromValue = props.p_from_mw;
		if (pFromValue !== undefined && pFromValue !== null) {
			fromParts.push(`${pFromValue.toFixed(2)}MW`);
		}
	}
	if (config.showPQ) {
		const pFromValue = props.p_from_mw;
		const qFromValue = props.q_from_mvar;
		if (pFromValue !== undefined && pFromValue !== null && qFromValue !== undefined && qFromValue !== null) {
			fromParts.push(formatComplexNumber(pFromValue, qFromValue) + 'MW');
		}
	}

	// To端数据
	const toParts: string[] = [];
	if (config.showP) {
		const pToValue = props.p_to_mw;
		if (pToValue !== undefined && pToValue !== null) {
			toParts.push(`${pToValue.toFixed(2)}MW`);
		}
	}
	if (config.showPQ) {
		const pToValue = props.p_to_mw;
		const qToValue = props.q_to_mva;
		if (pToValue !== undefined && pToValue !== null && qToValue !== undefined && qToValue !== null) {
			toParts.push(formatComplexNumber(pToValue, qToValue) + 'MW');
		}
	}

	// 中间数据
	const middleParts: string[] = [];
	if (config.showLineName) {
		middleParts.push(data.name || '');
	}
	if (config.showLoading) {
		if (props.loading_percent !== undefined && props.loading_percent !== null) {
			middleParts.push(`${Number(props.loading_percent).toFixed(1)}%`);
		}
	}

	const fromText = fromParts.join(' ');
	const middleText = middleParts.join(' ');
	const toText = toParts.join(' ');

	const displayParts = [fromText, middleText, toText].filter((part) => part.trim().length > 0);
	return displayParts.join('  ');
};

// ===== 模板设置 =====

/**
 * 设置节点模板
 */
const setupNodeTemplates = () => {
	if (!myDiagram || !$) return;

	// 变电站节点模板
	const stationTemplate = $(
		go.Node,
		'Spot',
		{
			locationSpot: go.Spot.Center,
			locationObjectName: 'MAIN_PANEL',
			cursor: 'pointer',
			selectionAdorned: false,
			selectionObjectName: 'MAIN_PANEL',
			selectionAdornmentTemplate: $(
				go.Adornment,
				'Spot',
				{
					locationSpot: go.Spot.Center,
				},
				$(go.Shape, 'Circle', {
					fill: null,
					stroke: COLORS.DEFAULT_LINE,
					strokeWidth: 3,
					strokeDashArray: [10, 5],
					width: 26 * ZOOM + 6,
					height: 26 * ZOOM + 6,
				})
			),
		},
		new go.Binding('click', '', (node) => {
			return (e: go.InputEvent, obj: go.GraphObject) => {
				const nodeData = obj.part?.data as SubstationNode;
				if (nodeData) {
					selectedNodeData.value = nodeData;
					emit('node-click', nodeData);
				}
			};
		}),
		new go.Binding('doubleClick', '', (node) => {
			return (e: go.InputEvent, obj: go.GraphObject) => {
				const nodeData = obj.part?.data as SubstationNode;
				if (nodeData) {
					selectedNodeData.value = nodeData;
					emit('node-double-click', nodeData);
				}
			};
		}),
		new go.Binding('location', '', (data: SubstationNode) => {
			if (data.x !== undefined && data.y !== undefined && !isNaN(Number(data.x)) && !isNaN(Number(data.y))) {
				return new go.Point(Number(data.x), Number(data.y));
			}
			return new go.Point(0, 0);
		}),
		$(
			go.Panel,
			'Spot',
			{
				name: 'MAIN_PANEL',
				isPanelMain: true,
				itemTemplate: $(
					go.Panel,
					'Spot',
					$(
						go.Shape,
						'Circle',
						{
							fill: COLORS.WHITE,
							strokeWidth: 1.5,
						},
						new go.Binding('portId', '', (data, obj) => {
							const panel = obj.panel;
							const index = panel?.itemIndex ?? 0;
							const isOuter = data.isOuter || false;
							return isOuter ? '' : null;
						}),
						new go.Binding('width', 'diameter'),
						new go.Binding('height', 'diameter'),
						new go.Binding('stroke'),
						new go.Binding('strokeWidth')
					)
				),
			},
			new go.Binding('itemArray', '', (data) => getInnerCircles(data.voltage || '')),
			$(
				go.Panel,
				'Vertical',
				{
					alignment: go.Spot.Top,
					alignmentFocus: go.Spot.Bottom,
					isPanelMain: false,
					pickable: false,
				},
				new go.Binding('margin', '', (data: SubstationNode) => {
					const scaledRadius = (26 * ZOOM) / 2;
					const upwardOffset = -(scaledRadius + 15);
					return new go.Margin(0, 2, upwardOffset * 3.6, 2);
				}),
				// 节点名称
				$(
					go.TextBlock,
					{
						font: 'bold 10px 宋体',
						stroke: COLORS.DEFAULT,
						textAlign: 'center',
						margin: new go.Margin(2, 4, 2, 4),
						background: 'rgba(255, 255, 255, 0.9)',
						editable: false,
					},
					new go.Binding('text', 'name'),
					new go.Binding('stroke', 'color')
				),
				// U值显示
				$(
					go.TextBlock,
					{
						font: '8px 宋体',
						stroke: COLORS.DEFAULT,
						textAlign: 'center',
						margin: new go.Margin(1, 4, 1, 4),
						background: 'rgba(255, 255, 255, 0.8)',
						editable: false,
					},
					new go.Binding('text', '', (data: SubstationNode) => {
						const props: any = data.properties || {};
						return props['U'] || '';
					}),
					new go.Binding('visible', '', (data: SubstationNode) => {
						const props: any = data.properties || {};
						const value = props['U'];
						return !!(value !== undefined && value !== null && String(value).trim());
					}),
					new go.Binding('stroke', 'color')
				),
				// U(M)值显示
				$(
					go.TextBlock,
					{
						font: '8px 宋体',
						stroke: COLORS.DEFAULT,
						textAlign: 'center',
						margin: new go.Margin(1, 4, 1, 4),
						background: 'rgba(255, 255, 255, 0.8)',
						editable: false,
					},
					new go.Binding('text', '', (data: SubstationNode) => {
						const props: any = data.properties || {};
						return props['U(M)'] || '';
					}),
					new go.Binding('visible', '', (data: SubstationNode) => {
						const props: any = data.properties || {};
						const value = props['U(M)'];
						return !!(value !== undefined && value !== null && String(value).trim());
					}),
					new go.Binding('stroke', 'color')
				)
			)
		)
	);

	// 发电厂模板
	const plantTemplate = $(
		go.Node,
		'Spot',
		{
			locationSpot: go.Spot.Center,
			locationObjectName: 'OUTER_SHAPE',
			cursor: 'pointer',
			selectionAdorned: false,
			selectionObjectName: 'OUTER_SHAPE',
			selectionAdornmentTemplate: $(
				go.Adornment,
				'Spot',
				{
					locationSpot: go.Spot.Center,
				},
				$(go.Shape, 'Circle', {
					fill: null,
					stroke: COLORS.DEFAULT_LINE,
					strokeWidth: 3,
					strokeDashArray: [10, 5],
					width: 26 * ZOOM + 6,
					height: 26 * ZOOM + 6,
				})
			),
		},
		new go.Binding('location', '', (data: SubstationNode) => {
			if (data.x !== undefined && data.y !== undefined && !isNaN(Number(data.x)) && !isNaN(Number(data.y))) {
				return new go.Point(Number(data.x), Number(data.y));
			}
			return new go.Point(0, 0);
		}),

		new go.Binding('click', '', (node) => {
			return (e: go.InputEvent, obj: go.GraphObject) => {
				const nodeData = obj.part?.data as SubstationNode;
				if (nodeData) {
					emit('node-click', nodeData);
				}
			};
		}),
		new go.Binding('doubleClick', '', (node) => {
			return (e: go.InputEvent, obj: go.GraphObject) => {
				const nodeData = obj.part?.data as SubstationNode;
				if (nodeData) {
					emit('node-double-click', nodeData);
				}
			};
		}),
		$(go.Shape, 'Circle', {
			name: 'OUTER_SHAPE',
			width: 26 * ZOOM,
			height: 26 * ZOOM,
			fill: COLORS.WHITE,
			strokeWidth: 1.5,
			stroke: COLORS.BLACK,
			portId: '',
		}),
		$(go.Shape, {
			geometryString: 'M -15 0 C -5 -10 5 10 15 0',
			stroke: COLORS.BLACK,
			strokeWidth: 1.5,
			width: 30,
			height: 15,
			fill: null,
		}),
		$(
			go.Panel,
			'Vertical',
			{
				alignment: go.Spot.Top,
				alignmentFocus: go.Spot.Bottom,
				margin: new go.Margin(0, 2, -15, 2),
				isPanelMain: false,
				pickable: false,
			},
			// 节点名称
			$(
				go.TextBlock,
				{
					font: 'bold 10px 宋体',
					stroke: COLORS.DEFAULT,
					textAlign: 'center',
					margin: new go.Margin(2, 4, 2, 4),
					background: 'rgba(255, 255, 255, 0.9)',
					editable: false,
				},
				new go.Binding('text', 'name')
			),
			// U值显示
			$(
				go.TextBlock,
				{
					font: '8px 宋体',
					stroke: COLORS.DEFAULT,
					textAlign: 'center',
					margin: new go.Margin(1, 4, 1, 4),
					background: 'rgba(255, 255, 255, 0.8)',
					editable: false,
				},
				new go.Binding('text', '', (data: SubstationNode) => {
					const props: any = data.properties || {};
					return props['U'] || '';
				}),
				new go.Binding('visible', '', (data: SubstationNode) => {
					const props: any = data.properties || {};
					const value = props['U'];
					return !!(value !== undefined && value !== null && String(value).trim());
				})
			),
			// U(M)值显示
			$(
				go.TextBlock,
				{
					font: '8px 宋体',
					stroke: COLORS.DEFAULT,
					textAlign: 'center',
					margin: new go.Margin(1, 4, 1, 4),
					background: 'rgba(255, 255, 255, 0.8)',
					editable: false,
				},
				new go.Binding('text', '', (data: SubstationNode) => {
					const props: any = data.properties || {};
					return props['U(M)'] || '';
				}),
				new go.Binding('visible', '', (data: SubstationNode) => {
					const props: any = data.properties || {};
					const value = props['U(M)'];
					return !!(value !== undefined && value !== null && String(value).trim());
				})
			)
		)
	);

	const templmap = new go.Map<string, go.Part>();
	templmap.add('station', stationTemplate);
	templmap.add('plant', plantTemplate);
	myDiagram.nodeTemplateMap = templmap;
};

/**
 * 设置连接线模板
 */
const setupLinkTemplate = () => {
	if (!myDiagram || !$) return;

	myDiagram.linkTemplate = new ParallelRouteLink({
		relinkableFrom: true,
		relinkableTo: true,
		reshapable: true,
		curve: go.Link.JumpOver,
		layerName: 'Background',
	})
		.add(
			new go.Shape({
				strokeWidth: 1,
				stroke: COLORS.KV_500,
				name: 'SHAPE',
				strokeDashArray: [0],
			})
				.bind('stroke', 'color')
				.bind('strokeDashArray', '', () => [0])
				.bind('contextClick', '', (data: SubstationLink) => {
					return props.enableLinkContextMenu
						? (e: go.InputEvent, obj: go.GraphObject) => {
								// 阻止默认右键菜单
								e.handled = true;
								// 获取鼠标位置
								const mouseEvent = e.event as MouseEvent;
								if (mouseEvent) {
									showLinkContextMenuAt(mouseEvent.clientX, mouseEvent.clientY, data);
								}
						  }
						: null;
				})
		)
		.add(
			new go.Shape({
				toArrow: 'OpenTriangle',
				stroke: COLORS.KV_500,
				fill: COLORS.KV_500,
				scale: 1.5,
				segmentIndex: 0,
				segmentFraction: 0.5,
				segmentOrientation: go.Link.OrientAlong,
			})
				.bind('stroke', 'color')
				.bind('fill', 'color')
				.bind('scale', '', () => 1.5)
				.bind('segmentIndex', '', (data: SubstationLink) => {
					const lineCount = data.lineCount || 1;
					return lineCount > 1 ? 1 : 0;
				})
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
				})
				.bind('visible', '', (data: SubstationLink) => {
					const sourceId = data?.source || '';
					const direction = data?.direction || 'forward';
					const showForwardArrow = (data.from === sourceId && direction === 'forward') || (data.from !== sourceId && direction === 'backward');
					return showForwardArrow;
				})
		)
		.add(
			new go.Shape({
				fromArrow: 'BackwardOpenTriangle',
				stroke: COLORS.KV_500,
				fill: COLORS.KV_500,
				scale: 1.5,
				segmentIndex: 0,
				segmentFraction: 0.5,
				segmentOrientation: go.Link.OrientAlong,
			})
				.bind('stroke', 'color')
				.bind('fill', 'color')
				.bind('scale', '', () => 1.5)
				.bind('segmentIndex', '', (data: SubstationLink) => {
					const lineCount = data.lineCount || 1;
					return lineCount > 1 ? 1 : 0;
				})
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
				})
				.bind('visible', '', (data: SubstationLink) => {
					const sourceId = data?.source || '';
					const direction = data?.direction || 'forward';
					const showBackwardArrow = (data.from === sourceId && direction === 'backward') || (data.from !== sourceId && direction === 'forward');
					return showBackwardArrow;
				})
		)
		.add(
			new go.TextBlock({
				segmentOffset: new go.Point(10, -10),
				font: '9px 宋体',
				stroke: COLORS.DEFAULT,
				background: 'rgba(255, 255, 255, 0.9)',
				segmentOrientation: go.Link.OrientUpright,
			})
				.bind('text', '', (data: SubstationLink) => formatLineDataSingle(data))
				.bind('segmentIndex', '', (data: SubstationLink) => {
					const lineCount = data.lineCount || 1;
					return lineCount > 1 ? 1 : 0;
				})
				.bind('segmentFraction', '', (data: SubstationLink) => {
					return getLineSegmentFraction(data.lineCount || 1, data.lineIndex || 0);
				})
				.bind('stroke', '', (data: SubstationLink) => data.color)
		);
};

/**
 * 设置图表布局
 */
const setupLayout = () => {
	if (!myDiagram) return;

	const layout = myDiagram.layout as go.ForceDirectedLayout;
	layout.defaultSpringLength = 250;
	layout.defaultElectricalCharge = 350;
	layout.maxIterations = 300;

	myDiagram.layout.isInitial = true;
	myDiagram.layout.isOngoing = true;
};

// ===== 数据过滤逻辑 =====

/**
 * 分析节点数据，提取电压和地区分组信息
 */
const analyzeNodeData = (nodes: SubstationNode[]): void => {
	// 电压分组数据收集
	const voltageMap = new Map<string, SubstationNode[]>();
	// 地区分组数据收集
	const zoneMap = new Map<string, SubstationNode[]>();

	nodes.forEach((node) => {
		// 按电压等级分组
		const voltage = node.voltage || '未知电压';
		if (!voltageMap.has(voltage)) {
			voltageMap.set(voltage, []);
		}
		voltageMap.get(voltage)!.push(node);

		// 按地区分组
		const zoneName = (node as any).zone || '未知地区';
		if (!zoneMap.has(zoneName)) {
			zoneMap.set(zoneName, []);
		}
		zoneMap.get(zoneName)!.push(node);
	});

	// 生成电压分组数据
	groupedData.value.voltages = Array.from(voltageMap.entries())
		.map(([voltage, nodeList]) => ({
			value: voltage,
			label: voltage,
			count: nodeList.length,
			nodes: [...nodeList],
		}))
		.sort((a, b) => {
			// 按电压等级排序
			const getVoltageNum = (v: string) => {
				const match = v.match(/(\d+)/);
				return match ? parseInt(match[1]) : 0;
			};
			return getVoltageNum(b.value) - getVoltageNum(a.value);
		});

	// 生成地区分组数据
	groupedData.value.zones = Array.from(zoneMap.entries())
		.map(([zone, nodeList]) => ({
			value: zone,
			label: zone,
			count: nodeList.length,
			nodes: [...nodeList],
		}))
		.sort((a, b) => a.label.localeCompare(b.label, 'zh'));

	console.log('数据分析完成:', {
		voltages: groupedData.value.voltages.map((v) => ({ label: v.label, count: v.count, nodeCount: v.nodes.length })),
		zones: groupedData.value.zones.map((z) => ({ label: z.label, count: z.count, nodeCount: z.nodes.length })),
	});
};

/**
 * 重置过滤条件
 */
const resetFilter = (): void => {
	filterConditions.value.voltages = [];
	filterConditions.value.zones = [];
	// 计算属性会自动重新计算，不需要手动调用过滤函数
};

/**
 * 切换过滤面板显示状态
 */
const toggleFilterPanel = (): void => {
	showFilterPanel.value = !showFilterPanel.value;
};

/**
 * 获取Map格式的电压分组数据（仅数量）
 * @returns Map<string, number> 电压等级 -> 节点数量
 */
const getVoltageGroupMap = (): Map<string, number> => {
	const voltageMap = new Map<string, number>();
	originalNodeData.value.forEach((node) => {
		const voltage = node.voltage || '未知电压';
		voltageMap.set(voltage, (voltageMap.get(voltage) || 0) + 1);
	});
	return voltageMap;
};

/**
 * 获取Map格式的地区分组数据（仅数量）
 * @returns Map<string, number> 地区名称 -> 节点数量
 */
const getZoneGroupMap = (): Map<string, number> => {
	const zoneMap = new Map<string, number>();
	originalNodeData.value.forEach((node) => {
		const zoneName = (node as any).zone || '未知地区';
		zoneMap.set(zoneName, (zoneMap.get(zoneName) || 0) + 1);
	});
	return zoneMap;
};

/**
 * 获取Map格式的电压分组数据（包含节点列表）
 * @returns Map<string, SubstationNode[]> 电压等级 -> 节点列表
 */
const getVoltageGroupWithNodesMap = (): Map<string, SubstationNode[]> => {
	const voltageMap = new Map<string, SubstationNode[]>();
	originalNodeData.value.forEach((node) => {
		const voltage = node.voltage || '未知电压';
		if (!voltageMap.has(voltage)) {
			voltageMap.set(voltage, []);
		}
		voltageMap.get(voltage)!.push(node);
	});
	return voltageMap;
};

/**
 * 获取Map格式的地区分组数据（包含节点列表）
 * @returns Map<string, SubstationNode[]> 地区名称 -> 节点列表
 */
const getZoneGroupWithNodesMap = (): Map<string, SubstationNode[]> => {
	const zoneMap = new Map<string, SubstationNode[]>();
	originalNodeData.value.forEach((node) => {
		const zoneName = (node as any).zone || '未知地区';
		if (!zoneMap.has(zoneName)) {
			zoneMap.set(zoneName, []);
		}
		zoneMap.get(zoneName)!.push(node);
	});
	return zoneMap;
};

/**
 * 获取完整的分组数据Map
 * @returns { voltages: Map<string, number>, zones: Map<string, number> }
 */
const getGroupDataMaps = () => ({
	voltages: getVoltageGroupMap(),
	zones: getZoneGroupMap(),
});

/**
 * 获取完整的分组数据Map（包含节点列表）
 * @returns { voltages: Map<string, SubstationNode[]>, zones: Map<string, SubstationNode[]> }
 */
const getGroupDataWithNodesMaps = () => ({
	voltages: getVoltageGroupWithNodesMap(),
	zones: getZoneGroupWithNodesMap(),
});

// ===== 数据差异检测工具 =====

/**
 * 检测数据变化，返回需要增加、删除、更新的节点和连接线
 */
const detectDataChanges = (
	newNodes: SubstationNode[],
	newLinks: SubstationLink[],
	oldNodes: SubstationNode[] = cachedNodeData.value,
	oldLinks: SubstationLink[] = cachedLinkData.value
) => {
	// 节点变化检测
	const oldNodeMap = new Map(oldNodes.map((node) => [getNodeKey(node), node]));
	const newNodeMap = new Map(newNodes.map((node) => [getNodeKey(node), node]));

	const nodesToAdd: SubstationNode[] = [];
	const nodesToRemove: SubstationNode[] = [];
	const nodesToUpdate: { old: SubstationNode; new: SubstationNode }[] = [];

	// 检测新增和更新的节点
	newNodes.forEach((newNode) => {
		const key = getNodeKey(newNode);
		const oldNode = oldNodeMap.get(key);
		if (!oldNode) {
			nodesToAdd.push(newNode);
		} else if (!isNodeDataEqual(oldNode, newNode)) {
			nodesToUpdate.push({ old: oldNode, new: newNode });
		}
	});

	// 检测删除的节点
	oldNodes.forEach((oldNode) => {
		const key = getNodeKey(oldNode);
		if (!newNodeMap.has(key)) {
			nodesToRemove.push(oldNode);
		}
	});

	// 连接线变化检测
	const oldLinkMap = new Map(oldLinks.map((link) => [getLinkKey(link), link]));
	const newLinkMap = new Map(newLinks.map((link) => [getLinkKey(link), link]));

	const linksToAdd: SubstationLink[] = [];
	const linksToRemove: SubstationLink[] = [];
	const linksToUpdate: { old: SubstationLink; new: SubstationLink }[] = [];

	// 检测新增和更新的连接线
	newLinks.forEach((newLink) => {
		const key = getLinkKey(newLink);
		const oldLink = oldLinkMap.get(key);
		if (!oldLink) {
			linksToAdd.push(newLink);
		} else if (!isLinkDataEqual(oldLink, newLink)) {
			linksToUpdate.push({ old: oldLink, new: newLink });
		}
	});

	// 检测删除的连接线
	oldLinks.forEach((oldLink) => {
		const key = getLinkKey(oldLink);
		if (!newLinkMap.has(key)) {
			linksToRemove.push(oldLink);
		}
	});

	return {
		nodes: { add: nodesToAdd, remove: nodesToRemove, update: nodesToUpdate },
		links: { add: linksToAdd, remove: linksToRemove, update: linksToUpdate },
	};
};

/**
 * 获取节点唯一标识
 */
const getNodeKey = (node: SubstationNode): string => {
	return (node as any).key || (node as any).id || `${node.name}-${node.x}-${node.y}`;
};

/**
 * 获取连接线唯一标识
 */
const getLinkKey = (link: SubstationLink): string => {
	return (link as any).key || link.name || `${link.from}-${link.to}-${link.lineIndex || 0}`;
};

/**
 * 比较两个节点数据是否相等
 */
const isNodeDataEqual = (node1: SubstationNode, node2: SubstationNode): boolean => {
	// 比较主要属性
	const keys = ['name', 'x', 'y', 'voltage', 'type', 'properties'];
	return keys.every((key) => {
		const val1 = (node1 as any)[key];
		const val2 = (node2 as any)[key];
		if (key === 'properties') {
			return JSON.stringify(val1) === JSON.stringify(val2);
		}
		return val1 === val2;
	});
};

/**
 * 比较两个连接线数据是否相等
 */
const isLinkDataEqual = (link1: SubstationLink, link2: SubstationLink): boolean => {
	// 比较主要属性
	const keys = ['name', 'from', 'to', 'color', 'voltage', 'direction', 'properties'];
	return keys.every((key) => {
		const val1 = (link1 as any)[key];
		const val2 = (link2 as any)[key];
		if (key === 'properties') {
			return JSON.stringify(val1) === JSON.stringify(val2);
		}
		return val1 === val2;
	});
};

// ===== 增量更新核心方法 =====

/**
 * 增量更新节点数据
 */
const updateNodesIncremental = (changes: {
	add: SubstationNode[];
	remove: SubstationNode[];
	update: { old: SubstationNode; new: SubstationNode }[];
}): boolean => {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法增量更新节点');
		return false;
	}

	try {
		myDiagram.startTransaction('incremental node update');

		// 删除节点
		changes.remove.forEach((node) => {
			const existingNode = myDiagram!.findNodeForKey(getNodeKey(node));
			if (existingNode) {
				myDiagram!.model.removeNodeData(existingNode.data);
			}
		});

		// 添加节点
		changes.add.forEach((node) => {
			const nodeWithKey = { ...node, key: getNodeKey(node) };
			myDiagram!.model.addNodeData(nodeWithKey);
		});

		// 更新节点
		changes.update.forEach(({ old, new: newNode }) => {
			const key = getNodeKey(old);
			const existingNode = myDiagram!.findNodeForKey(key);
			if (existingNode) {
				// 更新节点属性
				Object.keys(newNode).forEach((propKey) => {
					const newValue = (newNode as any)[propKey];
					const oldValue = (existingNode.data as any)[propKey];
					if (newValue !== oldValue) {
						myDiagram!.model.setDataProperty(existingNode.data, propKey, newValue);
					}
				});
			}
		});

		myDiagram.commitTransaction('incremental node update');
		console.log(`增量更新节点: +${changes.add.length} -${changes.remove.length} ~${changes.update.length}`);
		return true;
	} catch (error) {
		myDiagram.rollbackTransaction();
		console.error('增量更新节点失败:', error);
		return false;
	}
};

/**
 * 增量更新连接线数据
 */
const updateLinksIncremental = (changes: {
	add: SubstationLink[];
	remove: SubstationLink[];
	update: { old: SubstationLink; new: SubstationLink }[];
}): boolean => {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法增量更新连接线');
		return false;
	}

	try {
		myDiagram.startTransaction('incremental link update');

		// 删除连接线
		changes.remove.forEach((link) => {
			const linkKey = getLinkKey(link);
			myDiagram!.links.each((existingLink) => {
				if (getLinkKey(existingLink.data as SubstationLink) === linkKey) {
					(myDiagram!.model as go.GraphLinksModel).removeLinkData(existingLink.data);
				}
			});
		});

		// 添加连接线
		const newLinks = convertToLinkData(changes.add);
		newLinks.forEach((link) => {
			const linkWithKey = { ...link, key: getLinkKey(link) };
			(myDiagram!.model as go.GraphLinksModel).addLinkData(linkWithKey);
		});

		// 更新连接线
		changes.update.forEach(({ old, new: newLink }) => {
			const linkKey = getLinkKey(old);
			myDiagram!.links.each((existingLink) => {
				if (getLinkKey(existingLink.data as SubstationLink) === linkKey) {
					// 更新连接线属性
					Object.keys(newLink).forEach((propKey) => {
						const newValue = (newLink as any)[propKey];
						const oldValue = (existingLink.data as any)[propKey];
						if (newValue !== oldValue) {
							myDiagram!.model.setDataProperty(existingLink.data, propKey, newValue);
						}
					});
				}
			});
		});

		myDiagram.commitTransaction('incremental link update');
		console.log(`增量更新连接线: +${changes.add.length} -${changes.remove.length} ~${changes.update.length}`);
		return true;
	} catch (error) {
		myDiagram.rollbackTransaction();
		console.error('增量更新连接线失败:', error);
		return false;
	}
};

/**
 * 增量更新图表数据（主要方法）
 */
const updateGraphDataIncremental = (
	nodeData: SubstationNode[],
	linkData: SubstationLink[],
	options: {
		forceLayout?: boolean;
		preserveViewport?: boolean;
		updateCache?: boolean;
	} = {}
): boolean => {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法增量更新数据');
		return false;
	}

	const { forceLayout = false, preserveViewport = true, updateCache = true } = options;

	try {
		// 检测数据变化
		const changes = detectDataChanges(nodeData, linkData);

		// 检查是否有变化
		const hasChanges =
			changes.nodes.add.length > 0 ||
			changes.nodes.remove.length > 0 ||
			changes.nodes.update.length > 0 ||
			changes.links.add.length > 0 ||
			changes.links.remove.length > 0 ||
			changes.links.update.length > 0;

		if (!hasChanges) {
			console.log('数据无变化，跳过更新');
			return true;
		}

		// 保存当前视图状态
		const currentScale = preserveViewport ? myDiagram.scale : null;
		const currentPosition = preserveViewport ? myDiagram.position.copy() : null;

		// 更新搜索节点数据
		allNodes.value = nodeData;

		// 执行增量更新
		const nodeUpdateSuccess = updateNodesIncremental(changes.nodes);
		const linkUpdateSuccess = updateLinksIncremental(changes.links);

		// 根据选项决定是否重新布局
		if (forceLayout && (changes.nodes.add.length > 0 || changes.nodes.remove.length > 0)) {
			myDiagram.layoutDiagram(true);
		}

		// 恢复视图状态
		if (preserveViewport && currentScale !== null && currentPosition !== null) {
			setTimeout(() => {
				if (myDiagram) {
					myDiagram.scale = currentScale;
					myDiagram.position = currentPosition;
				}
			}, 0);
		}

		// 更新缓存
		if (updateCache) {
			cachedNodeData.value = [...nodeData];
			cachedLinkData.value = [...linkData];
		}

		console.log('增量更新完成');
		return nodeUpdateSuccess && linkUpdateSuccess;
	} catch (error) {
		console.error('增量更新失败:', error);
		return false;
	}
};

// ===== 图表初始化 =====

/**
 * 初始化图表实例（不包含数据）
 */
const initDiagram = () => {
	if (!diagramRef.value || isInitialized.value) return;

	try {
		$ = go.GraphObject.make;

		myDiagram = $(go.Diagram, diagramRef.value, {
			initialContentAlignment: go.Spot.Center,
			'undoManager.isEnabled': true,
			'clickCreatingTool.isEnabled': false,
			'draggingTool.isGridSnapEnabled': true,
			layout: $(go.ForceDirectedLayout, {
				maxIterations: 200,
				defaultSpringLength: 150,
				defaultElectricalCharge: 200,
			}),
		});

		setupNodeTemplates();
		setupLinkTemplate();
		setupLayout();

		diagramInstance.value = myDiagram;
		isInitialized.value = true;

		console.log('图表实例初始化完成');
	} catch (error) {
		console.error('初始化图表实例失败:', error);
		isInitialized.value = false;
	}
};

/**
 * 初始化缩略图
 */
const initOverview = () => {
	if (!overviewRef.value || !myDiagram || !$) return;

	myOverview = $(go.Overview, overviewRef.value, {
		observed: myDiagram,
		contentAlignment: go.Spot.Center,
		box: $(
			go.Part,
			$(go.Shape, {
				fill: 'rgba(100, 149, 237, 0.2)',
				stroke: 'cornflowerblue',
				strokeWidth: 2,
			})
		),
	});
};

/**
 * 更新图表数据并重新渲染
 */
function updateGraphData(
	nodeData: SubstationNode[],
	linkData: SubstationLink[],
	options: {
		incremental?: boolean;
		forceLayout?: boolean;
		preserveViewport?: boolean;
	} = {}
) {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法更新数据');
		return;
	}

	const { incremental = false, forceLayout = true, preserveViewport = false } = options;

	// 如果启用增量更新，优先使用增量更新方法
	if (incremental) {
		const success = updateGraphDataIncremental(nodeData, linkData, {
			forceLayout,
			preserveViewport,
			updateCache: true,
		});

		if (success) {
			// 增量更新成功，不需要重新设置加载状态
			setTimeout(() => {
				loading.value = false;
			}, 100);
			return;
		} else {
			console.warn('增量更新失败，回退到完整更新模式');
		}
	}

	try {
		// 完整更新模式（原有逻辑）
		allNodes.value = nodeData;

		// 保存原始数据并分析
		originalNodeData.value = [...nodeData];
		originalLinkData.value = [...linkData];
		analyzeNodeData(nodeData);

		const links = convertToLinkData(linkData);

		// 创建新的数据模型
		const newModel = new go.GraphLinksModel(nodeData, links);
		myDiagram.model = newModel;

		// 根据选项决定是否触发布局重新计算
		if (forceLayout) {
			myDiagram.layoutDiagram(true);
		}

		// 延迟调整视图和停止加载状态
		setTimeout(
			() => {
				if (myDiagram) {
					if (!preserveViewport) {
						myDiagram.scale = 1;
						setTimeout(() => {
							if (myDiagram) {
								const docBounds = myDiagram.documentBounds;
								if (docBounds.width > 0 && docBounds.height > 0) {
									myDiagram.centerRect(docBounds);
								} else {
									myDiagram.scrollToRect(docBounds);
								}
							}
							loading.value = false;
						}, 100);
					} else {
						loading.value = false;
					}
				}
			},
			preserveViewport ? 100 : 1000
		);

		// 更新缓存
		cachedNodeData.value = [...nodeData];
		cachedLinkData.value = [...linkData];

		console.log('图表数据更新完成');
	} catch (error) {
		console.error('更新图表数据失败:', error);
		loading.value = false;
	}
}

/**
 * 转换连接线数据
 */
const convertToLinkData = (apiData: any[]): SubstationLink[] => {
	if (!apiData || !Array.isArray(apiData)) {
		console.warn('convertToLinkData: 输入数据无效');
		return [];
	}

	// 收集相同起点和终点的线路
	const routeGroups = new Map<string, any[]>();

	apiData.forEach((item) => {
		try {
			const fromId = String(item.source || '');
			const toId = String(item.target || '');

			if (!fromId || !toId) {
				console.warn('convertToLinkData: 线路缺少起点或终点', item);
				return;
			}

			const [sortedId1, sortedId2] = [fromId, toId].sort();
			const routeKey = `${sortedId1}-${sortedId2}`;

			if (!routeGroups.has(routeKey)) {
				routeGroups.set(routeKey, []);
			}
			routeGroups.get(routeKey)!.push(item);
		} catch (error) {
			console.error('convertToLinkData: 处理线路数据时出错', item, error);
		}
	});

	// 为每组线路分配适当的lineCount和lineIndex
	const result: SubstationLink[] = [];
	routeGroups.forEach((group, routeKey) => {
		const lineCount = group.length;
		const [sortedId1, sortedId2] = routeKey.split('-');

		group.forEach((item, index) => {
			try {
				// 处理路径点
				let points: Point[] = [];
				if (item.lineList) {
					try {
						const lineListData = typeof item.lineList === 'string' ? JSON.parse(item.lineList) : item.lineList;
						if (Array.isArray(lineListData)) {
							points = lineListData.map((point) => ({
								x: parseFloat(point.x || 0),
								y: parseFloat(point.y || 0),
							}));
						}
					} catch (error) {
						console.warn('convertToLinkData: 解析lineList失败', error);
					}
				}

				// 判断是否需要交换节点顺序
				const originalFrom = String(item.source || '');
				const needSwap = originalFrom !== sortedId1;

				const linkData: SubstationLink = {
					name: String(item.name || ''),
					from: sortedId1,
					to: sortedId2,
					source: String(item.source || ''),
					target: String(item.target || ''),
					color: String(item.color || '#00bcd4'),
					voltage: String(item.voltage || ''),
					direction: String(item.direction || 'forward'),
					lineCount: lineCount,
					lineIndex: index,
					points: needSwap ? [...points].reverse() : points,
					properties: item.properties || {},
				};
				result.push(linkData);
			} catch (error) {
				console.error('convertToLinkData: 创建线路数据时出错', item, error);
			}
		});
	});

	console.log(`convertToLinkData: 成功转换 ${result.length} 条线路数据`);
	return result;
};

/**
 * 初始化或重新初始化完整的图表（向后兼容）
 */
function initGraph(nodeData?: SubstationNode[], linkData?: SubstationLink[]) {
	// 确保图表实例已初始化
	if (!isInitialized.value) {
		initDiagram();
	}

	// 使用传入的数据或props数据
	const nodes = nodeData || props.node;
	const links = linkData || props.link;

	updateGraphData(nodes, links);
}

// ===== 右键菜单方法 =====

/**
 * 关闭右键菜单
 */
const closeLinkContextMenu = () => {
	showLinkContextMenu.value = false;
	contextMenuPosition.value = null;
	contextMenuLinkData.value = null;
};

/**
 * 显示右键菜单
 */
const showLinkContextMenuAt = (x: number, y: number, linkData: SubstationLink) => {
	// 检查是否启用了右键菜单
	if (!props.enableLinkContextMenu) {
		return;
	}
	contextMenuPosition.value = { x, y };
	contextMenuLinkData.value = linkData;
	showLinkContextMenu.value = true;
};

// ===== 图表实例操作方法 =====

/**
 * 更新线路的所有视觉颜色元素（线路、箭头、文字）
 * @param link GoJS Link对象
 * @param color 新颜色值
 */
const updateLinkVisualColor = (link: go.Link, color: string): void => {
	// 更新主线路颜色
	const shape = link.findObject('SHAPE') as go.Shape;
	if (shape) {
		shape.stroke = color;
	}

	// 遍历Link的所有元素，更新箭头和文字颜色
	const iterator = link.elements;
	while (iterator.next()) {
		const obj = iterator.value;
		if (obj instanceof go.Shape) {
			// 检查是否是箭头（有toArrow或fromArrow属性）
			if (obj.toArrow !== 'None' || obj.fromArrow !== 'None') {
				obj.stroke = color;
				obj.fill = color;
			}
		} else if (obj instanceof go.TextBlock) {
			// 更新文字颜色
			obj.stroke = color;
		}
	}
};

/**
 * 通用的线路属性更新方法 - 更新单个线路的单个属性
 * @param linkKey 线路的唯一标识
 * @param propertyName 属性名称
 * @param propertyValue 属性值
 * @param transactionName 事务名称（可选）
 * @param updateDataModel 是否更新数据模型（默认false，仅更新视觉属性）
 * @returns 是否修改成功
 */
const updateLinkProperty = (
	linkKey: string,
	propertyName: string,
	propertyValue: any,
	transactionName?: string,
	updateDataModel: boolean = false
): boolean => {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法修改线路属性');
		return false;
	}

	// 定义视觉属性列表（这些属性的修改不需要重新布局）
	const visualProperties = ['color', 'strokeWidth', 'opacity', 'stroke', 'strokeDashArray', 'fill'];
	const isVisualProperty = visualProperties.includes(propertyName);

	try {
		// 开始事务
		const txName = transactionName || `update link ${propertyName}`;
		myDiagram.startTransaction(txName);

		// 查找对应的线路
		let linkFound = false;
		myDiagram.links.each((link) => {
			const linkData = link.data as SubstationLink;
			// 支持多种方式匹配线路
			const matches =
				(linkData as any).key === linkKey ||
				`${linkData.from}-${linkData.to}` === linkKey ||
				linkData.name === linkKey ||
				(linkData as any).id === linkKey;

			if (matches) {
				if (isVisualProperty && !updateDataModel) {
					// 直接修改GoJS Link对象的视觉属性，避免触发数据模型变化
					const shape = link.findObject('SHAPE') as go.Shape;

					if (propertyName === 'color') {
						// 特殊处理颜色：需要同时更新线路、箭头、文字的颜色
						updateLinkVisualColor(link, propertyValue);
					} else if (shape) {
						switch (propertyName) {
							case 'stroke':
								shape.stroke = propertyValue;
								break;
							case 'strokeWidth':
								shape.strokeWidth = propertyValue;
								break;
							case 'opacity':
								shape.opacity = propertyValue;
								break;
							case 'strokeDashArray':
								shape.strokeDashArray = propertyValue;
								break;
							case 'fill':
								shape.fill = propertyValue;
								break;
						}
					}
				} else {
					// 更新数据模型中的属性（可能触发重新布局）
					myDiagram!.model.setDataProperty(linkData, propertyName, propertyValue);
				}
				linkFound = true;
			}
		});

		// 提交事务
		myDiagram.commitTransaction(txName);

		if (linkFound) {
			console.log(`线路 ${linkKey} 的 ${propertyName} 已更新为`, propertyValue, isVisualProperty ? '(仅视觉更新)' : '(数据模型更新)');
			return true;
		} else {
			console.warn(`未找到线路: ${linkKey}`);
			return false;
		}
	} catch (error) {
		// 回滚事务
		myDiagram.rollbackTransaction();
		console.error(`修改线路 ${propertyName} 失败:`, error);
		return false;
	}
};

/**
 * 通用的线路属性更新方法 - 更新单个线路的多个属性
 * @param linkKey 线路的唯一标识
 * @param properties 属性对象 {propertyName: propertyValue}
 * @param transactionName 事务名称（可选）
 * @param updateDataModel 是否更新数据模型（默认false，仅更新视觉属性）
 * @returns 是否修改成功
 */
const updateLinkProperties = (
	linkKey: string,
	properties: Record<string, any>,
	transactionName?: string,
	updateDataModel: boolean = false
): boolean => {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法修改线路属性');
		return false;
	}

	if (!properties || Object.keys(properties).length === 0) {
		return true;
	}

	// 定义视觉属性列表
	const visualProperties = ['color', 'strokeWidth', 'opacity', 'stroke', 'strokeDashArray', 'fill'];

	try {
		// 开始事务
		const txName = transactionName || 'update link properties';
		myDiagram.startTransaction(txName);

		// 查找对应的线路
		let linkFound = false;
		myDiagram.links.each((link) => {
			const linkData = link.data as SubstationLink;
			// 支持多种方式匹配线路
			const matches =
				(linkData as any).key === linkKey ||
				`${linkData.from}-${linkData.to}` === linkKey ||
				linkData.name === linkKey ||
				(linkData as any).id === linkKey;

			if (matches) {
				const shape = link.findObject('SHAPE') as go.Shape;

				// 批量更新属性
				Object.entries(properties).forEach(([propertyName, propertyValue]) => {
					const isVisualProperty = visualProperties.includes(propertyName);

					if (isVisualProperty && !updateDataModel) {
						if (propertyName === 'color') {
							// 特殊处理颜色：需要同时更新线路、箭头、文字的颜色
							updateLinkVisualColor(link, propertyValue);
						} else if (shape) {
							// 处理其他视觉属性
							switch (propertyName) {
								case 'stroke':
									shape.stroke = propertyValue;
									break;
								case 'strokeWidth':
									shape.strokeWidth = propertyValue;
									break;
								case 'opacity':
									shape.opacity = propertyValue;
									break;
								case 'strokeDashArray':
									shape.strokeDashArray = propertyValue;
									break;
								case 'fill':
									shape.fill = propertyValue;
									break;
							}
						}
					} else {
						// 更新数据模型
						myDiagram!.model.setDataProperty(linkData, propertyName, propertyValue);
					}
				});
				linkFound = true;
			}
		});

		// 提交事务
		myDiagram.commitTransaction(txName);

		if (linkFound) {
			const visualPropsCount = Object.keys(properties).filter((key) => visualProperties.includes(key)).length;
			const dataPropsCount = Object.keys(properties).length - visualPropsCount;
			console.log(`线路 ${linkKey} 的属性已更新:`, properties, `(视觉: ${visualPropsCount}, 数据: ${dataPropsCount})`);
			return true;
		} else {
			console.warn(`未找到线路: ${linkKey}`);
			return false;
		}
	} catch (error) {
		// 回滚事务
		myDiagram.rollbackTransaction();
		console.error('修改线路属性失败:', error);
		return false;
	}
};

/**
 * 通用的批量线路属性更新方法
 * @param updates 更新配置数组 [{linkKey: string, properties: Record<string, any>}]
 * @param transactionName 事务名称（可选）
 * @param updateDataModel 是否更新数据模型（默认false，仅更新视觉属性）
 * @returns 成功更新的数量
 */
const updateMultipleLinksProperties = (
	updates: { linkKey: string; properties: Record<string, any> }[],
	transactionName?: string,
	updateDataModel: boolean = false
): number => {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法修改线路属性');
		return 0;
	}

	if (!updates || updates.length === 0) {
		return 0;
	}

	// 定义视觉属性列表
	const visualProperties = ['color', 'strokeWidth', 'opacity', 'stroke', 'strokeDashArray', 'fill'];

	try {
		// 开始事务
		const txName = transactionName || 'update multiple links properties';
		myDiagram.startTransaction(txName);

		let successCount = 0;

		// 遍历所有线路
		myDiagram.links.each((link) => {
			const linkData = link.data as SubstationLink;

			// 查找是否有匹配的更新配置
			const updateConfig = updates.find(
				(update) =>
					(linkData as any).key === update.linkKey ||
					`${linkData.from}-${linkData.to}` === update.linkKey ||
					linkData.name === update.linkKey ||
					(linkData as any).id === update.linkKey
			);

			if (updateConfig && updateConfig.properties) {
				const shape = link.findObject('SHAPE') as go.Shape;

				// 批量更新属性
				Object.entries(updateConfig.properties).forEach(([propertyName, propertyValue]) => {
					const isVisualProperty = visualProperties.includes(propertyName);

					if (isVisualProperty && !updateDataModel) {
						if (propertyName === 'color') {
							// 特殊处理颜色：需要同时更新线路、箭头、文字的颜色
							updateLinkVisualColor(link, propertyValue);
						} else if (shape) {
							// 处理其他视觉属性
							switch (propertyName) {
								case 'stroke':
									shape.stroke = propertyValue;
									break;
								case 'strokeWidth':
									shape.strokeWidth = propertyValue;
									break;
								case 'opacity':
									shape.opacity = propertyValue;
									break;
								case 'strokeDashArray':
									shape.strokeDashArray = propertyValue;
									break;
								case 'fill':
									shape.fill = propertyValue;
									break;
							}
						}
					} else {
						// 更新数据模型
						myDiagram!.model.setDataProperty(linkData, propertyName, propertyValue);
					}
				});
				successCount++;
			}
		});

		// 提交事务
		myDiagram.commitTransaction(txName);

		console.log(`成功更新 ${successCount} 条线路的属性 ${updateDataModel ? '(包含数据模型)' : '(仅视觉属性)'}`);
		return successCount;
	} catch (error) {
		// 回滚事务
		myDiagram.rollbackTransaction();
		console.error('批量修改线路属性失败:', error);
		return 0;
	}
};

/**
 * 通用的条件性线路属性更新方法
 * @param condition 筛选条件函数
 * @param properties 要更新的属性对象
 * @param transactionName 事务名称（可选）
 * @param updateDataModel 是否更新数据模型（默认false，仅更新视觉属性）
 * @returns 成功更新的数量
 */
const updateLinkPropertiesByCondition = (
	condition: (linkData: SubstationLink) => boolean,
	properties: Record<string, any>,
	transactionName?: string,
	updateDataModel: boolean = false
): number => {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法修改线路属性');
		return 0;
	}

	if (!properties || Object.keys(properties).length === 0) {
		return 0;
	}

	// 定义视觉属性列表
	const visualProperties = ['color', 'strokeWidth', 'opacity', 'stroke', 'strokeDashArray', 'fill'];

	try {
		// 开始事务
		const txName = transactionName || 'update links properties by condition';
		myDiagram.startTransaction(txName);

		let successCount = 0;

		// 遍历所有线路
		myDiagram.links.each((link) => {
			const linkData = link.data as SubstationLink;

			// 检查是否满足条件
			if (condition(linkData)) {
				const shape = link.findObject('SHAPE') as go.Shape;

				// 批量更新属性
				Object.entries(properties).forEach(([propertyName, propertyValue]) => {
					const isVisualProperty = visualProperties.includes(propertyName);

					if (isVisualProperty && !updateDataModel) {
						if (propertyName === 'color') {
							// 特殊处理颜色：需要同时更新线路、箭头、文字的颜色
							updateLinkVisualColor(link, propertyValue);
						} else if (shape) {
							// 处理其他视觉属性
							switch (propertyName) {
								case 'stroke':
									shape.stroke = propertyValue;
									break;
								case 'strokeWidth':
									shape.strokeWidth = propertyValue;
									break;
								case 'opacity':
									shape.opacity = propertyValue;
									break;
								case 'strokeDashArray':
									shape.strokeDashArray = propertyValue;
									break;
								case 'fill':
									shape.fill = propertyValue;
									break;
							}
						}
					} else {
						// 更新数据模型
						myDiagram!.model.setDataProperty(linkData, propertyName, propertyValue);
					}
				});
				successCount++;
			}
		});

		// 提交事务
		myDiagram.commitTransaction(txName);

		console.log(`成功更新 ${successCount} 条线路的属性 ${updateDataModel ? '(包含数据模型)' : '(仅视觉属性)'}`);
		return successCount;
	} catch (error) {
		// 回滚事务
		myDiagram.rollbackTransaction();
		console.error('按条件修改线路属性失败:', error);
		return 0;
	}
};

/**
 * 获取线路信息
 * @param linkKey 线路标识
 * @returns 线路数据或null
 */
const getLinkData = (linkKey: string): SubstationLink | null => {
	if (!myDiagram || !isInitialized.value) {
		return null;
	}

	let foundLinkData: SubstationLink | null = null;

	myDiagram.links.each((link) => {
		const linkData = link.data as SubstationLink;
		const matches =
			(linkData as any).key === linkKey ||
			`${linkData.from}-${linkData.to}` === linkKey ||
			linkData.name === linkKey ||
			(linkData as any).id === linkKey;

		if (matches) {
			foundLinkData = linkData;
		}
	});

	return foundLinkData;
};

/**
 * 获取所有线路信息
 * @returns 所有线路数据数组
 */
const getAllLinkData = (): SubstationLink[] => {
	if (!myDiagram || !isInitialized.value) {
		return [];
	}

	const allLinks: SubstationLink[] = [];
	myDiagram.links.each((link) => {
		allLinks.push(link.data as SubstationLink);
	});

	return allLinks;
};

// ===== 基于通用方法的便捷颜色操作 =====

/**
 * 修改单个线路的颜色（基于通用方法）
 * @param linkKey 线路的唯一标识
 * @param color 新的颜色值
 * @returns 是否修改成功
 */
const updateLinkColor = (linkKey: string, color: string): boolean => {
	return updateLinkProperty(linkKey, 'color', color, 'update link color');
};

/**
 * 批量修改多个线路的颜色（基于通用方法）
 * @param updates 更新配置数组 [{linkKey: string, color: string}]
 * @returns 成功更新的数量
 */
const updateMultipleLinkColors = (updates: { linkKey: string; color: string }[]): number => {
	const propertiesUpdates = updates.map((update) => ({
		linkKey: update.linkKey,
		properties: { color: update.color },
	}));
	return updateMultipleLinksProperties(propertiesUpdates, 'update multiple link colors');
};

/**
 * 根据条件修改线路颜色（基于通用方法）
 * @param condition 筛选条件函数
 * @param color 新的颜色值
 * @returns 成功更新的数量
 */
const updateLinkColorByCondition = (condition: (linkData: SubstationLink) => boolean, color: string): number => {
	return updateLinkPropertiesByCondition(condition, { color }, 'update link colors by condition');
};

/**
 * 重置所有线路颜色到默认值（基于通用方法）
 * @param defaultColor 默认颜色，如果不提供则使用配置文件中的颜色
 * @returns 重置的线路数量
 */
const resetAllLinkColors = (defaultColor?: string): number => {
	const resetColor = defaultColor || COLORS.KV_500;
	return updateLinkPropertiesByCondition(() => true, { color: resetColor }, 'reset all link colors');
};

/**
 * 高亮指定的线路（基于通用方法）
 * @param linkKeys 要高亮的线路标识数组
 * @param highlightColor 高亮颜色
 * @param restoreOthers 是否将其他线路恢复到默认样式
 * @returns 成功高亮的线路数量
 */
const highlightLinks = (linkKeys: string[], highlightColor: string = '#ff0000', restoreOthers: boolean = true): number => {
	if (!myDiagram || !isInitialized.value) {
		console.warn('图表实例未初始化，无法高亮线路');
		return 0;
	}

	const allLinks = getAllLinkData();
	const updates: { linkKey: string; properties: Record<string, any> }[] = [];

	allLinks.forEach((linkData) => {
		const linkKey = linkData.name || `${linkData.from}-${linkData.to}`;
		const shouldHighlight = linkKeys.some(
			(key) => (linkData as any).key === key || `${linkData.from}-${linkData.to}` === key || linkData.name === key || (linkData as any).id === key
		);

		if (shouldHighlight) {
			updates.push({ linkKey, properties: { color: highlightColor } });
		} else if (restoreOthers) {
			updates.push({ linkKey, properties: { color: COLORS.KV_500 } });
		}
	});

	return updateMultipleLinksProperties(updates, 'highlight links');
};

// ===== 更多便捷操作方法示例 =====

/**
 * 修改线路线宽
 * @param linkKey 线路标识
 * @param strokeWidth 线宽值
 * @returns 是否修改成功
 */
const updateLinkStrokeWidth = (linkKey: string, strokeWidth: number): boolean => {
	return updateLinkProperty(linkKey, 'strokeWidth', strokeWidth, 'update link stroke width');
};

/**
 * 修改线路透明度
 * @param linkKey 线路标识
 * @param opacity 透明度值 (0-1)
 * @returns 是否修改成功
 */
const updateLinkOpacity = (linkKey: string, opacity: number): boolean => {
	return updateLinkProperty(linkKey, 'opacity', opacity, 'update link opacity');
};

/**
 * 批量设置线路样式
 * @param linkKey 线路标识
 * @param style 样式对象 {color?, strokeWidth?, opacity?, dashArray?}
 * @returns 是否修改成功
 */
const updateLinkStyle = (linkKey: string, style: { color?: string; strokeWidth?: number; opacity?: number; dashArray?: number[] }): boolean => {
	return updateLinkProperties(linkKey, style, 'update link style');
};

// ===== 事件处理 =====

/**
 * 处理搜索组件的节点选择事件
 */
const handleSearchNodeSelected = (nodeData: SubstationNode) => {
	selectedNodeData.value = nodeData;
	emit('node-click', nodeData);
};

// ===== 视图控制 =====

const zoomIn = () => {
	if (!myDiagram) return;
	myDiagram.commandHandler.increaseZoom();
};

const zoomOut = () => {
	if (!myDiagram) return;
	myDiagram.commandHandler.decreaseZoom();
};

const resetZoom = () => {
	if (!myDiagram) return;
	myDiagram.zoomToFit();
	myDiagram.contentAlignment = go.Spot.Center;
};

// ===== 配置管理 =====

const openConfigDialog = () => {
	showConfigDialog.value = true;
};

const applyConfig = () => {
	showConfigDialog.value = false;
	if (!myDiagram) return;

	myDiagram.links.each((link) => {
		link.updateTargetBindings();
	});
};

// ===== 响应式数据监听 =====

/**
 * 响应式监听props数据变化，自动更新图表
 */
watchEffect(() => {
	// 确保DOM已挂载且图表已初始化
	if (diagramRef.value && isInitialized.value) {
		const hasValidData = props.node.length > 0 || props.link.length > 0;
		if (hasValidData) {
			updateGraphData(props.node, props.link);
		}
	}
});

// ===== 其他监听器 =====

watch(showOverview, (newValue) => {
	if (newValue) {
		setTimeout(() => {
			initOverview();
		}, 100);
	}
});

watch(
	lineIndicatorConfig,
	() => {
		if (myDiagram && isInitialized.value) {
			myDiagram.requestUpdate();
		}
	},
	{ deep: true }
);

// ===== 过滤数据监听器 =====

/**
 * 监听过滤后的数据变化，自动更新图表
 */
watch(
	[filteredNodeData, filteredLinkData],
	([newNodes, newLinks]) => {
		if (myDiagram && isInitialized.value && originalNodeData.value.length > 0) {
			// 使用增量更新，保持当前视图状态
			updateGraphData(newNodes, newLinks, {
				incremental: true,
				forceLayout: false,
				preserveViewport: true,
			});

			console.log(`过滤更新: 节点 ${newNodes.length}/${originalNodeData.value.length}, 连接线 ${newLinks.length}/${originalLinkData.value.length}`);
		}
	},
	{ immediate: false }
);
// ===== 组件生命周期 =====

onMounted(() => {
	// 初始化图表实例
	initDiagram();

	// 如果props中已有数据，立即更新
	nextTick(() => {
		const hasInitialData = props.node.length > 0 || props.link.length > 0;
		if (hasInitialData && isInitialized.value) {
			updateGraphData(props.node, props.link);
		}
	});
});

onBeforeUnmount(() => {
	// 组件销毁前清理资源
	destroyDiagram();
});

// ===== 组件接口 =====
defineExpose({
	diagram: diagramInstance,
	initGraph,
	updateGraphData,
	updateGraphDataIncremental,
	destroyDiagram,
	isInitialized: readonly(isInitialized),
	// 增量更新相关方法
	detectDataChanges,
	updateNodesIncremental,
	updateLinksIncremental,
	// 数据过滤相关方法
	analyzeNodeData,
	resetFilter,
	toggleFilterPanel,
	// Map格式分组数据
	getVoltageGroupMap,
	getZoneGroupMap,
	getGroupDataMaps,
	// Map格式分组数据（包含节点列表）
	getVoltageGroupWithNodesMap,
	getZoneGroupWithNodesMap,
	getGroupDataWithNodesMaps,
	// 过滤状态
	showFilterPanel: readonly(showFilterPanel),
	filterConditions: readonly(filterConditions),
	groupedData: readonly(groupedData),
	originalNodeData: readonly(originalNodeData),
	filteredNodeData: readonly(filteredNodeData),
	filteredLinkData: readonly(filteredLinkData),
	filterStats: readonly(filterStats),
	// 通用属性更新方法
	updateLinkProperty,
	updateLinkProperties,
	updateMultipleLinksProperties,
	updateLinkPropertiesByCondition,
	// 便捷颜色控制方法
	updateLinkColor,
	updateMultipleLinkColors,
	updateLinkColorByCondition,
	resetAllLinkColors,
	highlightLinks,
	// 更多便捷方法
	updateLinkStrokeWidth,
	updateLinkOpacity,
	updateLinkStyle,
	// 线路数据获取方法
	getLinkData,
	getAllLinkData,
	// 右键菜单控制方法
	closeLinkContextMenu,
	showLinkContextMenuAt,
});
</script>
