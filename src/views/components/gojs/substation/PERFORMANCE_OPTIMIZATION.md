# 性能优化指南

## 🎯 性能目标

- **初始渲染**: 600 节点 < 2 秒
- **增量更新**: < 100ms
- **交互响应**: < 16ms (60fps)
- **内存使用**: 稳定，无泄漏

## 🔍 性能问题分析

### 当前性能瓶颈 (600 节点场景)

1. **模板复杂度过高**
   - 节点模板包含多个 TextBlock 和复杂绑定
   - 每个节点创建 3-4 个文本元素
   - 大量的条件绑定计算

2. **布局算法性能**
   - ForceDirectedLayout 在大数据集下性能差
   - 默认迭代次数过高 (300 次)
   - 布局参数未优化

3. **数据绑定开销**
   - 过多的响应式绑定
   - 频繁的属性计算
   - 不必要的深度监听

4. **渲染策略问题**
   - 全量重绘而非增量更新
   - 视图状态未优化保存
   - 缺少虚拟化机制

## ⚡ 优化方案

### 1. 模板优化

#### 节点模板简化
```typescript
// 优化前：复杂模板
const stationTemplate = $(go.Node, 'Spot', {
  // 多个 TextBlock，复杂绑定
})

// 优化后：简化模板
const stationTemplate = $(go.Node, 'Spot', {
  // 合并文本显示，减少元素数量
  // 使用缓存的绑定函数
  // 延迟加载非关键信息
})
```

#### 文本合并策略
```typescript
// 将多个 TextBlock 合并为一个
const getNodeDisplayText = (data: SubstationNode): string => {
  const parts = [data.name]
  const props = data.properties || {}
  
  if (props.U) parts.push(props.U)
  if (props['U(M)']) parts.push(`(${props['U(M)']})`)
  
  return parts.join('\n')
}
```

### 2. 布局优化

#### 布局参数调优
```typescript
const setupOptimizedLayout = () => {
  if (!myDiagram) return
  
  const layout = myDiagram.layout as go.ForceDirectedLayout
  
  // 针对大数据集优化
  layout.maxIterations = 150        // 减少迭代次数
  layout.defaultSpringLength = 200  // 适中的弹簧长度
  layout.defaultElectricalCharge = 300 // 适中的电荷
  layout.infiniteDistance = 1000    // 限制计算距离
  
  // 启用增量布局
  layout.isInitial = false
  layout.isOngoing = false
}
```

#### 分层布局策略
```typescript
// 按电压等级分层布局
const setupLayeredLayout = () => {
  const layeredLayout = $(go.LayeredDigraphLayout, {
    direction: 0,
    layerSpacing: 100,
    columnSpacing: 50,
    setsPortSpots: false
  })
  
  myDiagram.layout = layeredLayout
}
```

### 3. 数据处理优化

#### 数据预处理
```typescript
// 数据预处理，减少运行时计算
const preprocessNodeData = (rawNodes: any[]): SubstationNode[] => {
  return rawNodes.map(node => ({
    ...node,
    // 预计算显示文本
    displayText: getNodeDisplayText(node),
    // 预计算样式
    computedStyle: getNodeStyle(node),
    // 添加索引用于快速查找
    _index: node.id
  }))
}

// 使用 Map 优化查找性能
const createNodeMap = (nodes: SubstationNode[]): Map<string, SubstationNode> => {
  return new Map(nodes.map(node => [node.id || node.name, node]))
}
```

#### 批量更新优化
```typescript
// 批量更新，减少事务次数
const batchUpdateNodes = (updates: NodeUpdate[]) => {
  if (!myDiagram || updates.length === 0) return
  
  myDiagram.startTransaction('batch update')
  
  // 按类型分组更新
  const groupedUpdates = groupBy(updates, 'type')
  
  Object.entries(groupedUpdates).forEach(([type, typeUpdates]) => {
    switch (type) {
      case 'color':
        updateNodeColors(typeUpdates)
        break
      case 'text':
        updateNodeTexts(typeUpdates)
        break
      // ... 其他类型
    }
  })
  
  myDiagram.commitTransaction('batch update')
}
```

### 4. 渲染优化

#### 虚拟化渲染
```typescript
// 视口裁剪，只渲染可见区域
const setupViewportCulling = () => {
  if (!myDiagram) return
  
  myDiagram.addDiagramListener('ViewportBoundsChanged', (e) => {
    const viewportBounds = myDiagram.viewportBounds
    
    // 隐藏视口外的节点
    myDiagram.nodes.each(node => {
      const nodeBounds = node.actualBounds
      const isVisible = viewportBounds.intersectsRect(nodeBounds)
      
      if (node.visible !== isVisible) {
        node.visible = isVisible
      }
    })
  })
}
```

#### 延迟加载
```typescript
// 延迟加载非关键信息
const setupLazyLoading = () => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const nodeElement = entry.target
        loadNodeDetails(nodeElement)
      }
    })
  })
  
  // 观察节点元素
  myDiagram.nodes.each(node => {
    observer.observe(node.elt(0))
  })
}
```

### 5. 内存优化

#### 对象池模式
```typescript
// 复用对象，减少 GC 压力
class NodePool {
  private pool: SubstationNode[] = []
  
  acquire(): SubstationNode {
    return this.pool.pop() || this.createNew()
  }
  
  release(node: SubstationNode): void {
    this.reset(node)
    this.pool.push(node)
  }
  
  private createNew(): SubstationNode {
    return { name: '', x: 0, y: 0 }
  }
  
  private reset(node: SubstationNode): void {
    // 重置对象状态
    Object.keys(node).forEach(key => {
      if (key !== 'name' && key !== 'x' && key !== 'y') {
        delete (node as any)[key]
      }
    })
  }
}
```

#### 弱引用清理
```typescript
// 使用 WeakMap 避免内存泄漏
const nodeCache = new WeakMap<HTMLElement, SubstationNode>()
const linkCache = new WeakMap<HTMLElement, SubstationLink>()

// 定期清理无用引用
const cleanupReferences = () => {
  // WeakMap 会自动清理无用引用
  console.log('缓存清理完成')
}
```

## 📊 性能监控

### 关键指标监控
```typescript
class PerformanceMonitor {
  private metrics = {
    renderTime: 0,
    nodeCount: 0,
    linkCount: 0,
    memoryUsage: 0,
    fps: 0
  }
  
  startRender(): void {
    this.renderStartTime = performance.now()
  }
  
  endRender(): void {
    this.metrics.renderTime = performance.now() - this.renderStartTime
    this.logMetrics()
  }
  
  private logMetrics(): void {
    console.table(this.metrics)
    
    // 性能警告
    if (this.metrics.renderTime > 2000) {
      console.warn('渲染时间过长:', this.metrics.renderTime)
    }
  }
}
```

### 实时性能面板
```vue
<template>
  <div v-if="showPerformancePanel" class="performance-panel">
    <h4>性能监控</h4>
    <div>渲染时间: {{ metrics.renderTime }}ms</div>
    <div>节点数量: {{ metrics.nodeCount }}</div>
    <div>连接线数量: {{ metrics.linkCount }}</div>
    <div>内存使用: {{ metrics.memoryUsage }}MB</div>
    <div>FPS: {{ metrics.fps }}</div>
  </div>
</template>
```

## 🚀 实施计划

### 阶段 1: 模板优化 (立即实施)
- [ ] 简化节点模板
- [ ] 合并文本显示
- [ ] 优化绑定函数
- [ ] 预期提升: 30-40%

### 阶段 2: 布局优化 (1 周内)
- [ ] 调优布局参数
- [ ] 实现分层布局
- [ ] 添加布局缓存
- [ ] 预期提升: 20-30%

### 阶段 3: 渲染优化 (2 周内)
- [ ] 实现虚拟化渲染
- [ ] 添加延迟加载
- [ ] 优化更新策略
- [ ] 预期提升: 40-50%

### 阶段 4: 内存优化 (3 周内)
- [ ] 实现对象池
- [ ] 优化缓存策略
- [ ] 添加内存监控
- [ ] 预期提升: 稳定性大幅提升

## 📈 预期效果

| 指标 | 优化前 | 优化后 | 提升 |
|------|--------|--------|------|
| 初始渲染 | 5-8s | 1.5-2s | 70% |
| 增量更新 | 300-500ms | 50-100ms | 80% |
| 内存使用 | 不稳定 | 稳定 | 显著 |
| 交互响应 | 卡顿 | 流畅 | 显著 |

## 🔧 配置建议

### 生产环境配置
```typescript
const productionConfig = {
  // 布局优化
  layout: {
    maxIterations: 100,
    springLength: 180,
    electricalCharge: 250
  },
  
  // 渲染优化
  rendering: {
    enableVirtualization: true,
    enableLazyLoading: true,
    batchSize: 50
  },
  
  // 内存优化
  memory: {
    enableObjectPool: true,
    cacheSize: 1000,
    gcInterval: 30000
  }
}
```

### 开发环境配置
```typescript
const developmentConfig = {
  ...productionConfig,
  
  // 开发调试
  debug: {
    enablePerformancePanel: true,
    logRenderTime: true,
    enableMemoryMonitor: true
  }
}
```
