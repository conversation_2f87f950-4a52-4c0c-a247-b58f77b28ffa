# 使用示例

## 🚀 基础使用

### 1. 简单的变电站图表

```vue
<template>
  <div class="h-screen">
    <SubstationDiagram
      :node="nodeData"
      :link="linkData"
      :loading="loading"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SubstationDiagram from '@/views/components/gojs/substation/index.vue'

const nodeData = ref([])
const linkData = ref([])
const loading = ref(true)

const handleNodeClick = (node) => {
  console.log('点击节点:', node.name)
}

onMounted(async () => {
  // 模拟数据加载
  nodeData.value = [
    { id: '1', name: '变电站A', x: 100, y: 100, voltage: '500kV', type: 'station' },
    { id: '2', name: '变电站B', x: 300, y: 200, voltage: '220kV', type: 'station' },
    { id: '3', name: '发电厂C', x: 200, y: 300, voltage: '500kV', type: 'plant' }
  ]
  
  linkData.value = [
    { name: '线路1', source: '1', target: '2', color: '#ff0000', voltage: '500kV' },
    { name: '线路2', source: '2', target: '3', color: '#00ff00', voltage: '220kV' }
  ]
  
  loading.value = false
})
</script>
```

### 2. 带性能监控的图表

```vue
<template>
  <div class="h-screen">
    <SubstationDiagram
      ref="diagramRef"
      :node="nodeData"
      :link="linkData"
      :loading="loading"
    />
    
    <!-- 性能监控面板 -->
    <div v-if="showPerformancePanel" class="performance-panel">
      <h4>性能监控</h4>
      <div>渲染时间: {{ metrics.renderTime.toFixed(2) }}ms</div>
      <div>节点数量: {{ metrics.nodeCount }}</div>
      <div>连接线数量: {{ metrics.linkCount }}</div>
      <div>内存使用: {{ metrics.memoryUsage }}MB</div>
      <div>FPS: {{ metrics.fps }}</div>
      <div>性能评级: {{ performanceGrade.grade }} ({{ performanceGrade.text }})</div>
      
      <div class="mt-2">
        <el-button size="small" @click="startMonitoring">开始监控</el-button>
        <el-button size="small" @click="stopMonitoring">停止监控</el-button>
        <el-button size="small" @click="generateReport">生成报告</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import SubstationDiagram from '@/views/components/gojs/substation/index.vue'

const diagramRef = ref()
const showPerformancePanel = ref(true)

// 性能监控数据
const metrics = computed(() => diagramRef.value?.performanceMonitor.metrics || {})
const performanceGrade = computed(() => diagramRef.value?.performanceMonitor.performanceGrade || {})

const startMonitoring = () => {
  diagramRef.value?.performanceMonitor.startMonitoring()
}

const stopMonitoring = () => {
  diagramRef.value?.performanceMonitor.stopMonitoring()
}

const generateReport = () => {
  const report = diagramRef.value?.performanceMonitor.generateReport()
  console.log(report)
}
</script>

<style scoped>
.performance-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 1000;
  min-width: 200px;
}
</style>
```

## 🎛️ 高级功能

### 3. 数据过滤和搜索

```vue
<template>
  <div class="h-screen">
    <SubstationDiagram
      ref="diagramRef"
      :node="nodeData"
      :link="linkData"
      :loading="loading"
      :filter-conditions="filterConditions"
    />
    
    <!-- 自定义过滤控制 -->
    <div class="filter-controls">
      <h4>数据过滤</h4>
      <div>
        <label>电压等级:</label>
        <el-checkbox-group v-model="filterConditions.voltages">
          <el-checkbox v-for="voltage in availableVoltages" :key="voltage" :label="voltage">
            {{ voltage }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      
      <div class="mt-2">
        <el-button size="small" @click="resetFilter">重置过滤</el-button>
        <el-button size="small" @click="exportFilteredData">导出过滤数据</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const diagramRef = ref()
const filterConditions = ref({
  voltages: ['500kV', '220kV'],
  zones: []
})

const availableVoltages = computed(() => {
  return [...new Set(nodeData.value.map(node => node.voltage))]
})

const resetFilter = () => {
  diagramRef.value?.resetFilter()
}

const exportFilteredData = () => {
  const filteredNodes = diagramRef.value?.filteredNodeData
  console.log('过滤后的节点:', filteredNodes)
}
</script>
```

### 4. 连接线操作和样式控制

```vue
<template>
  <div class="h-screen">
    <SubstationDiagram
      ref="diagramRef"
      :node="nodeData"
      :link="linkData"
      :enable-link-context-menu="true"
    >
      <!-- 自定义右键菜单 -->
      <template #link-context-menu="{ linkData, closeMenu }">
        <div class="custom-context-menu">
          <div class="menu-item" @click="highlightLine(linkData)">高亮线路</div>
          <div class="menu-item" @click="showLineDetails(linkData)">查看详情</div>
          <div class="menu-item" @click="changeLineColor(linkData)">更改颜色</div>
          <div class="menu-item" @click="closeMenu">关闭</div>
        </div>
      </template>
    </SubstationDiagram>
    
    <!-- 线路控制面板 -->
    <div class="line-controls">
      <h4>线路控制</h4>
      <el-button size="small" @click="highlightHighVoltageLines">高亮高压线路</el-button>
      <el-button size="small" @click="showOverloadedLines">显示过载线路</el-button>
      <el-button size="small" @click="resetAllLineColors">重置颜色</el-button>
    </div>
  </div>
</template>

<script setup>
const diagramRef = ref()

const highlightLine = (linkData) => {
  diagramRef.value?.updateLinkColor(linkData.name, '#ff0000')
}

const showLineDetails = (linkData) => {
  console.log('线路详情:', linkData)
}

const changeLineColor = (linkData) => {
  const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00']
  const randomColor = colors[Math.floor(Math.random() * colors.length)]
  diagramRef.value?.updateLinkColor(linkData.name, randomColor)
}

const highlightHighVoltageLines = () => {
  diagramRef.value?.updateLinkColorByCondition(
    (linkData) => linkData.voltage === '500kV',
    '#ff0000'
  )
}

const showOverloadedLines = () => {
  diagramRef.value?.updateLinkColorByCondition(
    (linkData) => {
      const loading = linkData.properties?.loading_percent || 0
      return loading > 80
    },
    '#ff6600'
  )
}

const resetAllLineColors = () => {
  diagramRef.value?.resetAllLinkColors()
}
</script>
```

### 5. 实时数据更新

```vue
<template>
  <div class="h-screen">
    <SubstationDiagram
      ref="diagramRef"
      :node="nodeData"
      :link="linkData"
      :loading="loading"
    />
    
    <div class="controls">
      <el-button @click="startRealTimeUpdate">开始实时更新</el-button>
      <el-button @click="stopRealTimeUpdate">停止更新</el-button>
      <el-button @click="simulateDataChange">模拟数据变化</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount } from 'vue'

const diagramRef = ref()
const updateInterval = ref(null)

// 开始实时数据更新
const startRealTimeUpdate = () => {
  if (updateInterval.value) return
  
  updateInterval.value = setInterval(() => {
    // 模拟实时数据更新
    updateNodeProperties()
    updateLinkProperties()
  }, 2000)
}

const stopRealTimeUpdate = () => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
    updateInterval.value = null
  }
}

// 更新节点属性（高频场景）
const updateNodeProperties = () => {
  const updates = nodeData.value.map(node => ({
    nodeKey: node.id,
    properties: {
      properties: {
        ...node.properties,
        U: (Math.random() * 100 + 400).toFixed(2) + 'kV'
      }
    }
  }))
  
  // 使用增量更新，性能更好
  diagramRef.value?.updateNodeProperties?.(updates)
}

// 更新连接线属性
const updateLinkProperties = () => {
  const updates = linkData.value.map(link => ({
    linkKey: link.name,
    properties: {
      properties: {
        ...link.properties,
        loading_percent: Math.random() * 100
      }
    }
  }))
  
  diagramRef.value?.updateLinkProperties?.(updates)
}

// 模拟数据变化
const simulateDataChange = () => {
  // 随机改变一些节点的颜色
  const randomNodes = nodeData.value.slice(0, 3)
  randomNodes.forEach(node => {
    const colors = ['#ff0000', '#00ff00', '#0000ff']
    const randomColor = colors[Math.floor(Math.random() * colors.length)]
    
    diagramRef.value?.updateNodeProperties?.([{
      nodeKey: node.id,
      properties: { color: randomColor }
    }])
  })
}

onBeforeUnmount(() => {
  stopRealTimeUpdate()
})
</script>
```

## 🔧 性能优化示例

### 6. 大数据集优化

```vue
<template>
  <div class="h-screen">
    <SubstationDiagram
      ref="diagramRef"
      :node="nodeData"
      :link="linkData"
      :loading="loading"
      :auto-initialize="false"
    />
    
    <div class="optimization-controls">
      <h4>性能优化控制</h4>
      <el-button @click="loadLargeDataset">加载大数据集 (1000+ 节点)</el-button>
      <el-button @click="enableVirtualization">启用虚拟化</el-button>
      <el-button @click="optimizeLayout">优化布局</el-button>
      <el-button @click="runPerformanceTest">性能测试</el-button>
    </div>
  </div>
</template>

<script setup>
const diagramRef = ref()

// 加载大数据集
const loadLargeDataset = async () => {
  loading.value = true
  
  // 开始性能监控
  diagramRef.value?.performanceMonitor.startMonitoring()
  
  try {
    // 生成大量测试数据
    const nodes = generateLargeNodeDataset(1000)
    const links = generateLargeLinkDataset(nodes, 2000)
    
    // 使用优化的更新方式
    await diagramRef.value?.updateGraphData(nodes, links, {
      incremental: false,
      forceLayout: true,
      preserveViewport: false
    })
    
  } finally {
    loading.value = false
  }
}

// 生成大量节点数据
const generateLargeNodeDataset = (count) => {
  const nodes = []
  const voltages = ['500kV', '220kV', '110kV', '35kV']
  const zones = ['华北', '华东', '华中', '华南', '西北', '东北']
  
  for (let i = 0; i < count; i++) {
    nodes.push({
      id: `node_${i}`,
      name: `节点${i}`,
      x: Math.random() * 2000,
      y: Math.random() * 1500,
      voltage: voltages[Math.floor(Math.random() * voltages.length)],
      zone: zones[Math.floor(Math.random() * zones.length)],
      type: Math.random() > 0.8 ? 'plant' : 'station',
      properties: {
        U: (Math.random() * 100 + 400).toFixed(2) + 'kV'
      }
    })
  }
  
  return nodes
}

// 运行性能测试
const runPerformanceTest = async () => {
  const tests = [
    {
      name: '渲染1000节点',
      fn: () => loadLargeDataset()
    },
    {
      name: '批量更新属性',
      fn: () => updateNodeProperties()
    },
    {
      name: '数据过滤',
      fn: () => diagramRef.value?.resetFilter()
    }
  ]
  
  await diagramRef.value?.performanceMonitor.runPerformanceTest(tests)
}
</script>
```

## 📊 数据格式示例

### 完整的节点数据示例

```javascript
const nodeExample = {
  id: 'station_001',
  name: '北京500kV变电站',
  x: 116.4074,
  y: 39.9042,
  voltage: '500kV',
  type: 'station',
  zone: '华北',
  properties: {
    U: '525.6kV',
    'U(M)': '523.2kV',
    capacity: '1000MVA',
    status: 'online',
    operator: '国家电网'
  }
}
```

### 完整的连接线数据示例

```javascript
const linkExample = {
  name: '京津500kV线路',
  source: 'station_001',
  target: 'station_002',
  color: '#ff0000',
  voltage: '500kV',
  direction: 'forward',
  lineList: [
    { x: 116.4074, y: 39.9042 },
    { x: 117.2008, y: 39.1036 }
  ],
  properties: {
    p_from_mw: 450.5,
    q_from_mvar: 120.3,
    p_to_mw: 445.2,
    q_to_mvar: 118.7,
    loading_percent: 75.6,
    length_km: 120,
    resistance: 0.025,
    reactance: 0.35
  }
}
```

## 🎯 最佳实践

1. **数据预处理**: 在传入组件前完成数据清洗
2. **增量更新**: 优先使用增量更新而非全量重绘
3. **性能监控**: 在生产环境中启用性能监控
4. **合理分页**: 超过1000节点考虑分页显示
5. **错误处理**: 添加完善的错误边界
