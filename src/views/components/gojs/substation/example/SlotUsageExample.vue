<template>
	<div class="w-full h-full">
		<!-- 控制面板 -->
		<div class="absolute top-16 left-4 z-20 bg-white p-4 rounded-lg shadow-lg border max-w-sm">
			<h3 class="text-lg font-semibold mb-3">图表控制面板</h3>

			<!-- 右键菜单控制 -->
			<div class="mb-4">
				<h4 class="text-sm font-medium mb-2">右键菜单控制</h4>
				<div class="space-y-2">
					<el-checkbox v-model="enableContextMenu" @change="updateContextMenuSettings"> 启用右键菜单 </el-checkbox>
					<div v-if="enableContextMenu" class="ml-4 space-y-1">
						<el-checkbox v-model="contextMenuConfig.enableViewDetails" @change="updateContextMenuSettings"> 查看详情 </el-checkbox>
						<el-checkbox v-model="contextMenuConfig.enableEdit" @change="updateContextMenuSettings"> 编辑功能 </el-checkbox>
						<el-checkbox v-model="contextMenuConfig.enableColorSetting" @change="updateContextMenuSettings"> 颜色设置 </el-checkbox>
						<el-checkbox v-model="contextMenuConfig.enableDelete" @change="updateContextMenuSettings"> 删除操作 </el-checkbox>
					</div>
				</div>
			</div>

			<!-- 线路颜色控制 -->
			<div class="mb-4">
				<h4 class="text-sm font-medium mb-2">线路颜色控制</h4>
				<div class="space-y-2">
					<el-button size="small" type="primary" @click="demonstrateColorControl"> 演示动态改色 </el-button>
					<el-button size="small" type="success" @click="highlightImportantLines"> 高亮重要线路 </el-button>
					<el-button size="small" type="warning" @click="showLoadBasedColors"> 按负载着色 </el-button>
					<el-button size="small" @click="resetColors"> 重置颜色 </el-button>
				</div>
			</div>

			<!-- 通用属性控制演示 -->
			<div class="mb-4">
				<h4 class="text-sm font-medium mb-2">通用属性控制演示</h4>
				<div class="space-y-2">
					<el-button size="small" type="info" @click="demonstrateStrokeWidth"> 修改线宽 </el-button>
					<el-button size="small" type="info" @click="demonstrateOpacity"> 修改透明度 </el-button>
					<el-button size="small" type="info" @click="demonstrateMultipleProperties"> 批量修改样式 </el-button>
					<el-button size="small" type="danger" @click="resetAllStyles"> 重置所有样式 </el-button>
				</div>
			</div>

			<!-- 线路信息显示 -->
			<div class="mb-4">
				<h4 class="text-sm font-medium mb-2">线路信息</h4>
				<div class="text-xs bg-gray-50 p-2 rounded max-h-20 overflow-y-auto">
					<div>总线路数: {{ allLinkCount }}</div>
					<div>已修改颜色: {{ modifiedLinkCount }}</div>
				</div>
			</div>
		</div>

		<!-- 使用厂站图组件，并自定义右键菜单插槽 -->
		<SubstationGraph
			ref="substationGraphRef"
			:node="nodeData"
			:link="linkData"
			:enable-link-context-menu="enableContextMenu"
			:context-menu-config="contextMenuConfig"
			v-model:loading="loading"
			@node-click="handleNodeClick"
			@node-double-click="handleNodeDoubleClick"
		>
			<!-- 自定义线路右键菜单插槽 -->
			<template #link-context-menu="{ linkData, closeMenu }">
				<div class="py-1">
					<!-- 线路信息标题 -->
					<div class="px-3 py-2 text-sm font-medium text-gray-700 border-b">
						{{ linkData?.name || '未命名线路' }}
						<span class="text-xs text-gray-500 ml-2">{{ linkData?.voltage }}kV</span>
					</div>

					<!-- 基础操作 -->
					<div class="py-1" v-if="contextMenuConfig">
						<div
							v-if="contextMenuConfig.enableViewDetails"
							class="px-3 py-2 text-sm cursor-pointer hover:bg-blue-50 flex items-center"
							@click="viewLineDetails(linkData, closeMenu)"
						>
							<el-icon class="mr-2 text-blue-600"><View /></el-icon>
							<span>查看线路详情</span>
						</div>

						<div
							v-if="contextMenuConfig.enableEdit"
							class="px-3 py-2 text-sm cursor-pointer hover:bg-green-50 flex items-center"
							@click="editLineProperties(linkData, closeMenu)"
						>
							<el-icon class="mr-2 text-green-600"><Edit /></el-icon>
							<span>编辑线路属性</span>
						</div>

						<div
							v-if="contextMenuConfig.enableCopy"
							class="px-3 py-2 text-sm cursor-pointer hover:bg-purple-50 flex items-center"
							@click="copyLineData(linkData, closeMenu)"
						>
							<el-icon class="mr-2 text-purple-600"><CopyDocument /></el-icon>
							<span>复制线路数据</span>
						</div>
					</div>

					<!-- 状态控制 -->
					<div class="border-t py-1" v-if="linkData && contextMenuConfig?.enableStatusControl">
						<div
							v-if="isLineOffService(linkData)"
							class="px-3 py-2 text-sm cursor-pointer hover:bg-green-50 flex items-center"
							@click="switchLineOn(linkData, closeMenu)"
						>
							<el-icon class="mr-2 text-green-600"><SwitchButton /></el-icon>
							<span>投运线路</span>
						</div>

						<div
							v-if="isLineInService(linkData)"
							class="px-3 py-2 text-sm cursor-pointer hover:bg-orange-50 flex items-center"
							@click="switchLineOff(linkData, closeMenu)"
						>
							<el-icon class="mr-2 text-orange-600"><SwitchButton /></el-icon>
							<span>停运线路</span>
						</div>
					</div>

					<!-- 高级操作 -->
					<div class="border-t py-1" v-if="contextMenuConfig">
						<div
							v-if="contextMenuConfig.enableColorSetting"
							class="px-3 py-2 text-sm cursor-pointer hover:bg-indigo-50 flex items-center"
							@click="setLineColor(linkData, closeMenu)"
						>
							<el-icon class="mr-2 text-indigo-600"><Brush /></el-icon>
							<span>设置线路颜色</span>
						</div>

						<div
							v-if="contextMenuConfig.enableLoadAnalysis"
							class="px-3 py-2 text-sm cursor-pointer hover:bg-yellow-50 flex items-center"
							@click="analyzeLineLoad(linkData, closeMenu)"
						>
							<el-icon class="mr-2 text-yellow-600"><TrendCharts /></el-icon>
							<span>负载分析</span>
						</div>
					</div>

					<!-- 危险操作 -->
					<div class="border-t py-1" v-if="contextMenuConfig?.enableDelete">
						<div class="px-3 py-2 text-sm cursor-pointer hover:bg-red-50 flex items-center text-red-600" @click="deleteLine(linkData, closeMenu)">
							<el-icon class="mr-2"><Delete /></el-icon>
							<span>删除线路</span>
						</div>
					</div>
				</div>
			</template>
		</SubstationGraph>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { View, Edit, CopyDocument, SwitchButton, Brush, TrendCharts, Delete } from '@element-plus/icons-vue';
import SubstationGraph from './index.vue';

// ===== 组件属性 =====
interface Props {
	nodeData: SubstationNode[];
	linkData: SubstationLink[];
}

const props = withDefaults(defineProps<Props>(), {
	nodeData: () => [],
	linkData: () => [],
});

// ===== 状态管理 =====
const loading = defineModel<boolean>('loading', { default: false });

// 右键菜单控制状态
const enableContextMenu = ref<boolean>(true);
const contextMenuConfig = ref<{
	enableViewDetails: boolean;
	enableEdit: boolean;
	enableCopy: boolean;
	enableStatusControl: boolean;
	enableColorSetting: boolean;
	enableLoadAnalysis: boolean;
	enableDelete: boolean;
}>({
	enableViewDetails: true,
	enableEdit: true,
	enableCopy: true,
	enableStatusControl: true,
	enableColorSetting: true,
	enableLoadAnalysis: true,
	enableDelete: true,
});

// 图表组件引用
const substationGraphRef = ref<InstanceType<typeof SubstationGraph> | null>(null);

// 线路统计信息
const allLinkCount = computed(() => {
	if (!substationGraphRef.value) return 0;
	return substationGraphRef.value.getAllLinkData().length;
});

const modifiedLinkCount = ref<number>(0);

// ===== 右键菜单控制方法 =====
const updateContextMenuSettings = () => {
	// 右键菜单设置更新时的逻辑
	console.log('右键菜单设置已更新:', { enableContextMenu: enableContextMenu.value, config: contextMenuConfig.value });
};

// ===== 线路颜色控制演示方法 =====

/**
 * 演示动态颜色控制 - 随机给几条线路换颜色
 */
const demonstrateColorControl = () => {
	if (!substationGraphRef.value) {
		ElMessage.warning('图表组件未准备就绪');
		return;
	}

	const allLinks = substationGraphRef.value.getAllLinkData();
	if (allLinks.length === 0) {
		ElMessage.warning('当前图表中没有线路');
		return;
	}

	// 随机选择几条线路进行颜色修改
	const colors = ['#ff0000', '#00ff00', '#0000ff', '#ff6600', '#9900ff', '#00ffff'];
	const selectedCount = Math.min(3, allLinks.length);
	const selectedIndices = new Set<number>();

	// 随机选择几条线路
	while (selectedIndices.size < selectedCount) {
		selectedIndices.add(Math.floor(Math.random() * allLinks.length));
	}

	const updates: { linkKey: string; color: string }[] = [];
	Array.from(selectedIndices).forEach((index, i) => {
		const link = allLinks[index];
		const linkKey = link.name || `${link.from}-${link.to}`;
		updates.push({
			linkKey,
			color: colors[i % colors.length],
		});
	});

	const successCount = substationGraphRef.value.updateMultipleLinkColors(updates);
	modifiedLinkCount.value += successCount;

	ElMessage.success(`成功为 ${successCount} 条线路设置了新颜色`);
};

/**
 * 高亮重要线路（模拟根据某种条件高亮）
 */
const highlightImportantLines = () => {
	if (!substationGraphRef.value) {
		ElMessage.warning('图表组件未准备就绪');
		return;
	}

	// 模拟筛选重要线路（这里以500kV线路为例）
	const importantLinks = substationGraphRef.value
		.getAllLinkData()
		.filter((link) => link.voltage === '500' || (link.properties as any)?.loading_percent > 80)
		.map((link) => link.name || `${link.from}-${link.to}`);

	if (importantLinks.length === 0) {
		ElMessage.info('没有找到符合条件的重要线路');
		return;
	}

	const highlightCount = substationGraphRef.value.highlightLinks(importantLinks, '#ff0000', true);
	modifiedLinkCount.value = substationGraphRef.value.getAllLinkData().length; // 所有线路都被处理了

	ElMessage.success(`成功高亮 ${highlightCount} 条重要线路`);
};

/**
 * 根据负载率设置线路颜色
 */
const showLoadBasedColors = () => {
	if (!substationGraphRef.value) {
		ElMessage.warning('图表组件未准备就绪');
		return;
	}

	// 按负载率分级着色
	const colorByLoadConditions = [
		{
			condition: (link: SubstationLink) => {
				const loadPercent = (link.properties as any)?.loading_percent || 0;
				return loadPercent >= 90;
			},
			color: '#ff0000', // 红色：高负载 ≥90%
		},
		{
			condition: (link: SubstationLink) => {
				const loadPercent = (link.properties as any)?.loading_percent || 0;
				return loadPercent >= 70 && loadPercent < 90;
			},
			color: '#ff6600', // 橙色：中高负载 70%-89%
		},
		{
			condition: (link: SubstationLink) => {
				const loadPercent = (link.properties as any)?.loading_percent || 0;
				return loadPercent >= 50 && loadPercent < 70;
			},
			color: '#ffcc00', // 黄色：中负载 50%-69%
		},
		{
			condition: (link: SubstationLink) => {
				const loadPercent = (link.properties as any)?.loading_percent || 0;
				return loadPercent < 50;
			},
			color: '#00aa00', // 绿色：低负载 <50%
		},
	];

	let totalUpdated = 0;
	colorByLoadConditions.forEach(({ condition, color }) => {
		const count = substationGraphRef.value!.updateLinkColorByCondition(condition, color);
		totalUpdated += count;
	});

	modifiedLinkCount.value = totalUpdated;
	ElMessage.success(`根据负载率为 ${totalUpdated} 条线路设置了颜色`);
};

/**
 * 重置所有线路颜色
 */
const resetColors = () => {
	if (!substationGraphRef.value) {
		ElMessage.warning('图表组件未准备就绪');
		return;
	}

	const resetCount = substationGraphRef.value.resetAllLinkColors();
	modifiedLinkCount.value = 0;

	ElMessage.success(`成功重置 ${resetCount} 条线路的颜色`);
};

// ===== 通用属性控制演示方法 =====

/**
 * 演示修改线路线宽
 */
const demonstrateStrokeWidth = () => {
	if (!substationGraphRef.value) {
		ElMessage.warning('图表组件未准备就绪');
		return;
	}

	const allLinks = substationGraphRef.value.getAllLinkData();
	if (allLinks.length === 0) {
		ElMessage.warning('当前图表中没有线路');
		return;
	}

	// 随机选择几条线路修改线宽
	const selectedLinks = allLinks.slice(0, Math.min(3, allLinks.length));

	selectedLinks.forEach((link, index) => {
		const strokeWidth = 2 + index * 2; // 线宽2, 4, 6
		const linkKey = link.name || `${link.from}-${link.to}`;
		substationGraphRef.value?.updateLinkStrokeWidth(linkKey, strokeWidth);
	});

	ElMessage.success(`已修改 ${selectedLinks.length} 条线路的线宽`);
};

/**
 * 演示修改线路透明度
 */
const demonstrateOpacity = () => {
	if (!substationGraphRef.value) {
		ElMessage.warning('图表组件未准备就绪');
		return;
	}

	const allLinks = substationGraphRef.value.getAllLinkData();
	if (allLinks.length === 0) {
		ElMessage.warning('当前图表中没有线路');
		return;
	}

	// 随机选择几条线路修改透明度
	const selectedLinks = allLinks.slice(0, Math.min(4, allLinks.length));

	selectedLinks.forEach((link, index) => {
		const opacity = 0.3 + index * 0.2; // 透明度 0.3, 0.5, 0.7, 0.9
		const linkKey = link.name || `${link.from}-${link.to}`;
		substationGraphRef.value?.updateLinkOpacity(linkKey, opacity);
	});

	ElMessage.success(`已修改 ${selectedLinks.length} 条线路的透明度`);
};

/**
 * 演示批量修改多个属性
 */
const demonstrateMultipleProperties = () => {
	if (!substationGraphRef.value) {
		ElMessage.warning('图表组件未准备就绪');
		return;
	}

	const allLinks = substationGraphRef.value.getAllLinkData();
	if (allLinks.length === 0) {
		ElMessage.warning('当前图表中没有线路');
		return;
	}

	// 准备批量更新数据
	const updates = allLinks.slice(0, Math.min(5, allLinks.length)).map((link, index) => {
		const linkKey = link.name || `${link.from}-${link.to}`;
		return {
			linkKey,
			properties: {
				color: ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'][index],
				strokeWidth: 1 + index,
				opacity: 0.6 + index * 0.1,
			},
		};
	});

	const successCount = substationGraphRef.value.updateMultipleLinksProperties(updates);
	ElMessage.success(`成功批量修改 ${successCount} 条线路的多个属性`);
};

/**
 * 重置所有样式
 */
const resetAllStyles = () => {
	if (!substationGraphRef.value) {
		ElMessage.warning('图表组件未准备就绪');
		return;
	}

	// 重置所有线路的样式属性
	const successCount = substationGraphRef.value.updateLinkPropertiesByCondition(
		() => true, // 所有线路
		{
			color: '#00bcd4', // 默认颜色
			strokeWidth: 1, // 默认线宽
			opacity: 1, // 默认透明度
		}
	);

	modifiedLinkCount.value = 0; // 重置计数
	ElMessage.success(`成功重置 ${successCount} 条线路的所有样式`);
};

// ===== 线路状态判断 =====
const isLineInService = (linkData: SubstationLink | null): boolean => {
	if (!linkData) return false;
	const props = (linkData.properties as any) || {};
	return props.in_service === 1 || props.in_service === true;
};

const isLineOffService = (linkData: SubstationLink | null): boolean => {
	if (!linkData) return false;
	const props = (linkData.properties as any) || {};
	return props.in_service === 0 || props.in_service === false;
};

// ===== 右键菜单动作 =====

/**
 * 查看线路详情
 */
const viewLineDetails = (linkData: SubstationLink | null, closeMenu: () => void) => {
	if (!linkData) return;
	console.log('查看线路详情:', linkData);
	ElMessage.info(`查看线路 "${linkData.name}" 的详细信息`);
	closeMenu();

	// 这里可以打开详情弹窗
	// showLineDetailsDialog(linkData);
};

/**
 * 编辑线路属性
 */
const editLineProperties = (linkData: SubstationLink | null, closeMenu: () => void) => {
	if (!linkData) return;
	console.log('编辑线路属性:', linkData);
	ElMessage.info(`编辑线路 "${linkData.name}" 的属性`);
	closeMenu();

	// 这里可以打开编辑弹窗
	// showLineEditDialog(linkData);
};

/**
 * 复制线路数据
 */
const copyLineData = async (linkData: SubstationLink | null, closeMenu: () => void) => {
	if (!linkData) return;
	try {
		const lineInfo = {
			name: linkData.name,
			from: linkData.from,
			to: linkData.to,
			voltage: linkData.voltage,
			properties: linkData.properties,
		};

		await navigator.clipboard.writeText(JSON.stringify(lineInfo, null, 2));
		ElMessage.success('线路数据已复制到剪贴板');
		console.log('已复制线路数据:', lineInfo);
	} catch (error) {
		ElMessage.error('复制失败');
		console.error('复制线路数据失败:', error);
	}
	closeMenu();
};

/**
 * 投运线路
 */
const switchLineOn = (linkData: SubstationLink | null, closeMenu: () => void) => {
	if (!linkData) return;
	console.log('投运线路:', linkData);
	ElMessage.success(`线路 "${linkData.name}" 已投运`);
	closeMenu();

	// 这里可以调用API更新线路状态
	// updateLineStatus(linkData.id, 'in_service');
};

/**
 * 停运线路
 */
const switchLineOff = (linkData: SubstationLink | null, closeMenu: () => void) => {
	if (!linkData) return;
	console.log('停运线路:', linkData);
	ElMessage.warning(`线路 "${linkData.name}" 已停运`);
	closeMenu();

	// 这里可以调用API更新线路状态
	// updateLineStatus(linkData.id, 'out_of_service');
};

/**
 * 设置线路颜色（增强版本，使用实例方法）
 */
const setLineColor = async (linkData: SubstationLink | null, closeMenu: () => void) => {
	if (!linkData || !substationGraphRef.value) return;

	try {
		const { value: color } = await ElMessageBox.prompt('请选择线路颜色', '设置颜色', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			inputPlaceholder: '输入颜色值，如：#ff0000',
			inputPattern: /^#[0-9a-fA-F]{6}$/,
			inputErrorMessage: '请输入有效的颜色值（如：#ff0000）',
		});

		const linkKey = linkData.name || `${linkData.from}-${linkData.to}`;
		const success = substationGraphRef.value.updateLinkColor(linkKey, color);

		if (success) {
			modifiedLinkCount.value++;
			ElMessage.success(`线路 "${linkData.name}" 颜色已设置为 ${color}`);
		} else {
			ElMessage.error('设置线路颜色失败');
		}
	} catch {
		// 用户取消操作
	}
	closeMenu();
};

/**
 * 线路负载分析
 */
const analyzeLineLoad = (linkData: SubstationLink | null, closeMenu: () => void) => {
	if (!linkData) return;
	console.log('分析线路负载:', linkData);
	ElMessage.info(`正在分析线路 "${linkData.name}" 的负载情况...`);
	closeMenu();

	// 这里可以打开负载分析页面
	// showLoadAnalysisDialog(linkData);
};

/**
 * 删除线路
 */
const deleteLine = async (linkData: SubstationLink | null, closeMenu: () => void) => {
	if (!linkData) return;
	try {
		await ElMessageBox.confirm(`确定要删除线路 "${linkData.name}" 吗？此操作不可撤销。`, '删除确认', {
			confirmButtonText: '删除',
			cancelButtonText: '取消',
			type: 'warning',
			confirmButtonClass: 'el-button--danger',
		});

		console.log('删除线路:', linkData);
		ElMessage.success(`线路 "${linkData.name}" 已删除`);

		// 这里可以调用API删除线路
		// deleteLineById(linkData.id);
	} catch {
		// 用户取消删除
	}
	closeMenu();
};

// ===== 节点事件处理 =====
const handleNodeClick = (nodeData: SubstationNode) => {
	console.log('节点点击:', nodeData);
	ElMessage.info(`点击了节点: ${nodeData.name}`);
};

const handleNodeDoubleClick = (nodeData: SubstationNode) => {
	console.log('节点双击:', nodeData);
	ElMessage.info(`双击了节点: ${nodeData.name}`);
};

// ===== 组件初始化 =====
onMounted(() => {
	console.log('厂站图组件示例已加载，包含右键菜单控制和动态颜色功能');
});
</script>

<style scoped>
/* 如果需要额外的样式，可以在这里添加 */
</style>
