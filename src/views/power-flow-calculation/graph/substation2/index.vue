<template>
	<div class="h-full w-full">
		<SubstationGraph
			ref="substationGraphRef"
			:nodeData="nodeData"
			:linkData="linkData"
			@node-double-click="handleNodeDoubleClick"
			v-model:loading="loading"
		/>
		<!-- 单线图弹窗 -->
		<el-dialog
			destroy-on-close
			class="!p-0"
			fullscreen
			body-class="!h-full !max-h-full"
			header-class="!p-0"
			v-model="isShowNodeInfo"
			width="90%"
			height="90%"
		>
			<SingleLineGraph v-if="nodeInfo?.key" :node-info="nodeInfo" />
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { provide, computed, ref, onMounted, inject, ComputedRef } from 'vue';
import SubstationGraph from '/@/views/components/gojs/substation/SlotUsageExample.vue';
import { query_substation_graph } from '../../api';
import { determineNodeType, getVoltageColor } from '/@/config/GraphConfig';
import SingleLineGraph from '../../single-line/index.vue';
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;
const substationGraphRef = ref<InstanceType<typeof SubstationGraph> | null>(null);
const nodeData = ref<SubstationNode[]>([]);
const linkData = ref<SubstationLink[]>([]);
const isShowNodeInfo = ref(false);
const nodeInfo = ref<SubstationNode | null>(null);
const handleNodeDoubleClick = (data: SubstationNode) => {
	isShowNodeInfo.value = true;
	nodeInfo.value = data;
};
const loading = ref(true);
/**
 * 加载图表数据
 */
async function loadGraphData() {
	try {
		const { data } = await query_substation_graph({
			bb_case_id: dataPacket.value.id,
			bb_diagram_layer_id: '34',
		});
		nodeData.value = convertToNodeData(data.nodes);
		linkData.value = convertToLinkData(data.edges);
	} catch (error) {
		loading.value = false;
	}
}

/**
 * 转换节点数据
 */
const convertToNodeData = (data: any[]): SubstationNode[] => {
	return data.map(
		(item): SubstationNode => ({
			key: item.id,
			name: item.name,
			type: item.type,
			category: determineNodeType(item.type),
			voltage: item.voltage || '',
			properties: item.properties,
			color: getVoltageColor(item.voltage),
		})
	);
};

/**
 * 转换连接线数据
 */
const convertToLinkData = (apiData: any[]): SubstationLink[] => {
	// 收集相同起点和终点的线路
	const routeGroups = new Map<string, any[]>();

	apiData.forEach((item) => {
		const fromId = item.source || '';
		const toId = item.target || '';
		const [sortedId1, sortedId2] = [fromId, toId].sort();
		const routeKey = `${sortedId1}-${sortedId2}`;

		if (!routeGroups.has(routeKey)) {
			routeGroups.set(routeKey, []);
		}
		routeGroups.get(routeKey)!.push(item);
	});

	// 为每组线路分配适当的lineCount和lineIndex
	const result: SubstationLink[] = [];
	routeGroups.forEach((group, routeKey) => {
		const lineCount = group.length;
		const [sortedId1, sortedId2] = routeKey.split('-');

		group.forEach((item, index) => {
			let points: Point[] = [];
			if (item.lineList) {
				try {
					const lineListData = JSON.parse(item.lineList);
					if (Array.isArray(lineListData)) {
						points = lineListData.map((point) => ({
							x: parseFloat(point.x || 0),
							y: parseFloat(point.y || 0),
						}));
					}
				} catch (error) {
					console.error('解析lineList失败:', error);
				}
			}

			const fromId = item.source || '';
			const needSwap = fromId !== sortedId1;
			let direction = item.direction || 'forward';

			const linkData: SubstationLink = {
				name: item.name,
				from: sortedId1,
				to: sortedId2,
				source: item.source,
				target: item.target,
				color: getVoltageColor(item.voltage),
				voltage: item.voltage,
				direction: direction,
				lineCount: lineCount,
				lineIndex: index,
				points: needSwap ? [...points].reverse() : points,
				properties: item.properties,
			};
			result.push(linkData);
		});
	});
	return result;
};

onMounted(() => {
	loadGraphData();
});
</script>

<style lang="scss" scoped></style>
