<template>
	<div class="h-full w-full">
		<SubstationGraph
			ref="substationGraphRef"
			@node-double-click="handleNodeDoubleClick"
			v-model:loading="loading"
			:auto-initialize="true"
			:filter-conditions="{
				voltages: ['500kV', '1000kV'],
			}"
		/>
		<!-- 单线图弹窗 -->
		<el-dialog
			destroy-on-close
			class="!p-0"
			fullscreen
			body-class="!h-full !max-h-full"
			header-class="!p-0"
			v-model="isShowNodeInfo"
			width="90%"
			height="90%"
		>
			<SingleLineGraph
				:node-info="nodeInfo || {}"
				ref="singleLineGraphRef"
				:node="singleLineNodeData"
				:link="singleLineLinkData"
				v-model:loading="singleLineLoading"
			>
				<template #title>
					<div class="leading-[24px] font-bold">{{ nodeInfo?.name || '单线图' }}</div>
				</template>
			</SingleLineGraph>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, ComputedRef } from 'vue';
import { ElMessage } from 'element-plus';
import SubstationGraph from '/@/views/components/gojs/substation/index.vue';
// import SingleLineGraph from '/@/views/components/gojs/single-line/index.vue';
import SingleLineGraph from '../single-line/index.vue';
import mittBus from '/@/utils/mitt';
import { query_substation_graph, query_single_line_graph } from '../../api';
import { determineNodeType, getVoltageColor } from '/@/config/GraphConfig';
const dataPacket = inject('dataPacket') as ComputedRef<Recordable>;
const substationGraphRef = ref<InstanceType<typeof SubstationGraph> | null>(null);
const nodeData = ref<SubstationNode[]>([]);
const linkData = ref<SubstationLink[]>([]);
const isShowNodeInfo = ref(false);
const nodeInfo = ref<SubstationNode | null>(null);
const singleLineNodeData = ref<SingleLineNode[]>([]);
const singleLineLinkData = ref<SingleLineLink[]>([]);
const singleLineLoading = ref(false);

const handleNodeDoubleClick = async (data: SubstationNode) => {
	nodeInfo.value = data;
	isShowNodeInfo.value = true;
	await loadSingleLineGraph();
};
const loading = ref(true);
mittBus.on('powerFlowCalculation', (data) => {
	console.log(data, 'data');
});
loadSubstationData();
/**
 * 加载变电站图表数据
 * 由于启用了自动预初始化，图表容器已经显示，这里只需要加载数据
 */
async function loadSubstationData() {
	try {
		const { data } = await query_substation_graph({
			bb_case_id: dataPacket.value.id,
			bb_diagram_layer_id: '34',
		});

		nodeData.value = convertToNodeData(data.nodes);
		linkData.value = convertToLinkData(data.edges);

		substationGraphRef.value?.initGraph(nodeData.value, linkData.value);
	} catch (error) {
		console.error('获取变电站数据失败:', error);
	} finally {
	}
}

/**
 * 转换节点数据
 */
const convertToNodeData = (data: any[]): SubstationNode[] => {
	return data.map(
		(item): SubstationNode => ({
			key: item.id,
			name: item.name,
			type: item.type,
			category: determineNodeType(item.type),
			voltage: item.voltage || '',
			properties: item.properties,
			color: getVoltageColor(item.voltage),
			zone: item.zone,
		})
	);
};

/**
 * 转换连接线数据
 */
const convertToLinkData = (data: any[]): SubstationLink[] => {
	return data.map((item) => {
		return {
			name: item.name,
			source: item.source,
			target: item.target,
			color: getVoltageColor(item.voltage),
			voltage: item.voltage,
			properties: item.properties,
		};
	});
};

const singleLineGraphRef = ref<InstanceType<typeof SingleLineGraph> | null>(null);

async function loadSingleLineGraph() {
	singleLineLoading.value = true;
	try {
		const { data } = await query_single_line_graph({
			bb_case_id: dataPacket.value.id,
			substation_id: nodeInfo.value?.key,
		});

		// 外部预处理：将API数据转换为SingleLineGraph组件所需的格式
		const processedNodes: SingleLineNode[] = [];
		const processedLinks: SingleLineLink[] = [];

		// 处理节点数据
		if (data.nodes && Array.isArray(data.nodes)) {
			data.nodes.forEach((node: any) => {
				// 创建符合SingleLineGraph组件要求的节点数据格式
				const nodeData: SingleLineNode = {
					key: node.id,
					category: node.type,
					type: node.type,
					name: node.name || '未命名节点',
					color: getVoltageColor(node.voltage),
					pos: [0, 0],
					angle: node.angle || 0,
					voltage: node.voltage,
					voltage2: node.voltage2,
					voltage3: node.voltage3,
					color2: getVoltageColor(node.voltage2),
					color3: getVoltageColor(node.voltage3),
					width: node.type === 'BusbarSection' ? 150 : undefined,
					properties: node.properties || {},
				};
				processedNodes.push(nodeData);
			});
		}

		// 处理连线数据
		if (data.edges && Array.isArray(data.edges)) {
			data.edges.forEach((link: any, index: number) => {
				// 跳过没有源或目标的连线
				if (!link.source || !link.target) {
					console.warn(`第${index}个连线缺少源或目标节点，已跳过`);
					return;
				}

				// 创建符合SingleLineGraph组件要求的连线数据格式
				const linkData: SingleLineLink = {
					key: `link-${index}`,
					from: link.source,
					to: link.target,
					fromPort: link.source_port,
					toPort: link.target_port,
					properties: link.properties || {},
					voltage: link.voltage,
					color: getVoltageColor(link.voltage),
				};

				processedLinks.push(linkData);
			});
		}

		// 将处理后的数据传递给SingleLineGraph组件
		singleLineNodeData.value = processedNodes;
		singleLineLinkData.value = processedLinks;

		console.log('单线图数据已预处理完成', { nodes: processedNodes, links: processedLinks });
	} catch (error) {
		console.error('加载单线图失败:', error);
		ElMessage.error(`加载单线图失败: ${error instanceof Error ? error.message : '未知错误'}`);
	} finally {
		singleLineLoading.value = false;
	}
}
</script>

<style lang="scss" scoped></style>
