<script setup lang="ts">
import { ref, computed, watch, onMounted, provide, reactive, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useTitle } from '@vueuse/core';
// 模型
import Model from './configured/Model.vue';
// 静态安全分析
import StaticSecurityAnalysis from './static-security-analysis/index.vue';
// 调度员潮流
import DispatcherPowerFlow from './dispatcher-power-flow/index.vue';
// 灵敏度计算
import AgilityCalculation from './agility-calculation/index.vue';
// 潮流计算
import calculation from './calculation/index.vue';
// 结果
// import Result from './graph/substation2/index.vue';

import { getCaseInfo, type SiMu } from '/@/views/config/api';
import { NextLoading } from '/@/utils/loading';
import { power_flow_calculation } from './api';
import { ElMessage } from 'element-plus';

const title = useTitle();

onMounted(() => NextLoading.done(600));
const route = useRoute();
const router = useRouter();
const siMuInfo = ref<SiMu>({ id: '', market_rule_id: '', name: '', scene_model_id: '' });
const current = reactive({
	type: 'list',
	page: 'list',
});
watch(
	() => route.query,
	(newVal) => {
		current.type = (newVal.type as string) || 'list';
		current.page = (newVal.page as string) || 'list';
	},
	{
		immediate: true,
	}
);
async function getSiMuInfo() {
	const { id } = route.query;
	const res = await getCaseInfo(Number(id));
	siMuInfo.value = res.data;
	title.value = `模型-${res.data.name}`;
}
getSiMuInfo();

const currentComponent = computed(() => {
	// 模型
	if (['list', 'graph'].includes(current.page)) return Model;
	// 静态安全分析
	if (['static-security-analysis'].includes(current.page)) return StaticSecurityAnalysis;
	// 调度员潮流
	if (['dispatcher-power-flow'].includes(current.page)) return DispatcherPowerFlow;
	// 灵敏度计算
	if (['agility-calculation'].includes(current.page)) return AgilityCalculation;
	// 潮流计算
	if (['calculation'].includes(current.page)) return calculation;
	// 结果
	// if (['result'].includes(current.page)) return Result; 测试
});
function handleTabs({ page, type }: { page?: string; type?: string }) {
	switch (type) {
		case 'calculation':
			powerFlowCalculation();
			break;
		default:
			router.replace({ name: 'PowerFlowCalculation', query: { id: siMuInfo.value.id, page: page || current.page, type: type } });
	}
}
async function powerFlowCalculation() {
	updateLoading('calculation', true);
	const { code, message } = await power_flow_calculation({ bb_case_id: siMuInfo.value.id });
	if (code === 2000) {
		ElMessage.success(`计算成功`);
	} else {
		ElMessage.error(`计算失败:${message}`);
	}
	updateLoading('calculation', false);
}

function updateLoading(key: string, loading: boolean) {
	navConfig.value.forEach((item) => {
		item.items.forEach((subItem) => {
			if (subItem.subItems) {
				subItem.subItems.forEach((subItem) => {
					if (subItem.key === key) {
						subItem.loading = loading;
					}
				});
			}
		});
	});
}

const dataPacket = computed((): Recordable => {
	return {
		...siMuInfo.value,
	};
});
provide('dataPacket', dataPacket);

// 导航配置数据
const navConfig = ref<NavGroup[]>([
	{
		type: 'composite',
		items: [
			{
				key: 'model',
				label: '实例信息',
				subItems: [
					{
						key: 'list',
						label: '电子表格',
						icon: 'iconfont icon-liebiao',
						iconSize: 14,
						type: 'page',
					},
					{
						key: 'graph',
						label: '地理接线图',
						icon: 'iconfont icon-sutpc-ditufuwuqiguanli_huaban1',
						iconSize: 14,
						type: 'page',
					},
					{
						key: 'calculation',
						label: '潮流计算',
						icon: 'iconfont icon-kaishi',
						type: 'action',
						disabled: false,
						loading: false,
					},
				],
			},
		],
	},
	{
		type: 'simple',
		items: [
			{
				key: 'agility-calculation',
				label: '灵敏度计算',
				icon: 'iconfont icon-hanshu',
				type: 'action',
			},
			{
				key: 'result',
				label: '结果',
				icon: 'iconfont icon-jieguo',
				type: 'page',
			},
		],
	},
	{
		type: 'simple',
		items: [
			{
				key: 'static-security-analysis',
				label: '静态安全分析',
				icon: 'iconfont icon-jingtaianquanfenxi',
				type: 'page',
			},
			{
				key: 'dispatcher-power-flow',
				label: '调度员潮流',
				icon: 'iconfont icon-tiaoduyuanchaoliu',
				type: 'page',
			},
		],
	},
	{
		type: 'simple',
		items: [
			{
				key: 'fault-set',
				label: '故障集',
				icon: 'iconfont icon-a-weibiaoti-2_huaban1fuben6',
				iconSize: 20,
				type: 'page',
			},
			{
				key: 'configuration',
				label: '配置',
				icon: 'iconfont icon-xitong',
				iconSize: 20,
				type: 'page',
			},
		],
	},
]);

// 下拉菜单状态管理
const activeDropdown = ref<string | null>(null);

// 切换下拉菜单显示状态
function toggleDropdown(key: string, event: Event) {
	event.stopPropagation();
	activeDropdown.value = activeDropdown.value === key ? null : key;
}

// 关闭下拉菜单
function closeDropdown() {
	activeDropdown.value = null;
}

// 点击下拉菜单项
function handleDropdownItem(parentKey: string, itemKey: string) {
	handleTabs({ type: itemKey, page: parentKey });
	closeDropdown();
}

// 判断下拉菜单项是否激活
const isDropdownItemActive = (parentKey: string, itemKey: string) => {
	const combinedPage = `${parentKey}-${itemKey}`;
	return current.page + '-' + current.type === combinedPage;
};
// 点击外部区域关闭下拉菜单
onMounted(() => {
	document.addEventListener('click', closeDropdown);
});
</script>
<template>
	<div class="model-container bg-[#f7f7f7] h-full flex flex-col overflow-hidden">
		<div class="select-none bg-[#DFEBF9] p-2 flex rounded-md shadow-sm text-[#15428B] gap-2">
			<div
				v-for="group in navConfig"
				:key="group.title"
				class="flex items-center bg-[#C8D8ED] rounded-md border-[#567DB1] border-solid border-[1px] p-1 gap-2"
			>
				<div v-if="group.title" class="py-1 bg-[#BACBE1] h-full w-[20px] text-[#003180] flex items-center text-center rounded-md">
					{{ group.title }}
				</div>

				<div v-if="group.type === 'simple'" class="flex gap-2">
					<div
						v-for="item in group.items"
						:key="item.key"
						@click="handleTabs({ page: item.type === 'page' ? item.key : undefined, type: item.type === 'action' ? item.key : undefined })"
						class="btn"
						:class="{ 'btn-active': current.page === item.key }"
					>
						<div class="icon">
							<SvgIcon :name="item.icon" :size="item.iconSize || 20" />
							<div class="text-[12px]">{{ item.label }}</div>
						</div>
					</div>
				</div>

				<div v-else-if="group.type === 'composite'" class="flex gap-2">
					<div
						v-for="item in group.items"
						:key="item.key"
						class="rounded-lg border-[#BACBE1] border-solid border-[1px] cursor-pointer hover:bg-[#F8FCFF]"
						:class="{ 'btn-active': item.subItems?.some((sub) => current.page === sub.key) }"
					>
						<div class="flex items-center justify-center gap-2 px-3 py-2">
							<div
								v-for="subItem in item.subItems"
								:key="subItem.key"
								class="rounded-md icon hover-shadow flex items-center px-1 relative"
								:class="{ 'btn2-active': current.page === subItem.key }"
							>
								<!-- 主按钮 -->
								<div
									v-if="subItem.type === 'page'"
									@click="
										subItem.hasDropdown
											? toggleDropdown(subItem.key, $event)
											: handleTabs({ page: subItem.type === 'page' ? subItem.key : undefined })
									"
									class="flex items-center cursor-pointer"
								>
									<SvgIcon :name="subItem.icon" :size="subItem.iconSize || 14" />
									<div class="text-[12px] px-1">{{ subItem.label }}</div>
									<!-- 下拉箭头 -->
									<div
										v-if="subItem.hasDropdown"
										class="ml-1 w-0 h-0 border-l-[3px] border-r-[3px] border-t-[4px] border-l-transparent border-r-transparent border-t-current transition-transform duration-200"
										:class="{ 'rotate-180': activeDropdown === subItem.key }"
									></div>
								</div>
								<div v-else-if="subItem.type === 'action'" class="h-21px">
									<el-button
										text
										size="small"
										type="color"
										@click="handleTabs({ type: subItem.key })"
										:disabled="subItem.disabled"
										:loading="subItem.loading"
										style="
											--el-fill-color-light: #f8fcff;
											--el-button-text-color: #15428b;
											--el-button-size: 21px;
											padding: 0px;
											--el-button-disabled-text-color: #15428b;
										"
									>
										<SvgIcon v-if="!subItem.loading" :name="subItem.icon" :size="subItem.iconSize || 14" />
										<span class="px-1">
											{{ subItem.label }}
										</span>
									</el-button>
								</div>

								<!-- 下拉菜单 -->
								<div
									v-if="subItem.hasDropdown && activeDropdown === subItem.key"
									class="dropdown-menu absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-50 min-w-[80px]"
								>
									<div
										v-for="dropItem in subItem.dropdownItems"
										:key="dropItem.key"
										@click="handleDropdownItem(subItem.key, dropItem.key)"
										class="dropdown-item flex items-center px-3 py-2 text-[12px] hover:bg-gray-100 cursor-pointer"
										:class="{ 'dropdown-item-active': isDropdownItemActive(subItem.key, dropItem.key) }"
									>
										<SvgIcon v-if="dropItem.icon" :name="dropItem.icon" :size="dropItem.iconSize || 12" class="mr-2" />
										<span>{{ dropItem.label }}</span>
									</div>
								</div>
							</div>
						</div>
						<div class="bg-[#BACBE1] text-center rounded-b-md" :class="{ 'px-4': item.key === 'auxiliary-service-market' }">
							{{ item.label }}
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="flex-1 overflow-auto">
			<template v-if="siMuInfo.id">
				<KeepAlive>
					<component :is="currentComponent" :siMuInfo="siMuInfo" />
				</KeepAlive>
			</template>
			<template v-else>
				<div>加载中...</div>
			</template>
		</div>
	</div>
</template>
<style scoped>
.model-container {
	::v-deep(.el-button) {
		margin: 0px !important;
		i.el-icon,
		i.iconfont {
			font-size: 14px !important;
			margin: 0px !important;
		}
	}
}

.btn {
	@apply px-2 py-1 rounded-lg cursor-pointer hover:bg-[#F8FCFF] flex items-center justify-center;
}

.btn-active {
	@apply bg-[#F8FCFF];
}

.icon {
	@apply text-center min-w-[56px];
}

.hover-shadow {
	@apply hover:shadow-[0px_0px_5px_2px_rgba(175,217,255,0.63)];
}

.btn2-active {
	@apply shadow-[0px_0px_5px_2px_rgba(175,217,255,0.63)];
}

/* 下拉菜单样式 */
.dropdown-menu {
	animation: dropdown-fade-in 0.15s ease-out;
}

.dropdown-item {
	transition: background-color 0.15s ease;
}

.dropdown-item-active {
	background-color: var(--el-color-primary-light-8) !important;
	color: var(--el-color-primary) !important;
}

.dropdown-item:first-child {
	border-radius: 0.375rem 0.375rem 0 0;
}

.dropdown-item:last-child {
	border-radius: 0 0 0.375rem 0.375rem;
}

@keyframes dropdown-fade-in {
	from {
		opacity: 0;
		transform: translateY(-4px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}
</style>
